import { SavedProposalsConfig } from 'types/moduleConfig';

const proposalConfig: SavedProposalsConfig = {
  stages: {
    QUICK_SI: true,
    FNA: true,
    CFF: false,
    FULL_SI: true,
    IN_APP: true,
  },
  clientType: {
    INDIVIDUAL: false,
    ENTITY: false,
  },
  // disable save new quick si for 10 times limit on re-select prodcut
  saveNewQuotation: {
    onCreateQuickSi: false,
    onCreateFullSi: true,
    fromSavedQuickSi: false,
    fromSavedFullSi: true,
  },
};

export default proposalConfig;
