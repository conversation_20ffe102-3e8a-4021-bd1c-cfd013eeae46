import { Policy } from 'types/team';

export type HeaderConfig = {
  ratio: number;
  label: string;
  field: keyof Policy;
  isTruncated?: boolean;
};

export const leftHeaderConfig: HeaderConfig[] = [
  { ratio: 176, label: 'Policy #', field: 'policyNum' },
];
export const rightHeaderConfig: HeaderConfig[] = [
  {
    ratio: 168,
    label: 'Life assured',
    field: 'lifeAssured',
    isTruncated: true,
  },
  { ratio: 168, label: 'Policy owner', field: 'policyOwner' },
  { ratio: 120, label: 'Policy status', field: 'policyStatus' },
  {
    ratio: 140,
    label: 'Policy premium payment status',
    field: 'premiumStatus',
  },
  { ratio: 136, label: 'Premium payment mode', field: 'payMode' },
  { ratio: 164, label: 'Modal Premium', field: 'modalPremium' },
  { ratio: 124, label: 'Due date', field: 'nextDueDate' },
  {
    ratio: 144,
    label: 'Last payment installment date',
    field: 'lastDueDate',
  },
  { ratio: 144, label: 'Premium  missing', field: 'balanceModalPremium' },
];
