import { useTheme } from '@emotion/react';
import { Body, Column, H6 } from 'cube-ui-components';

const Title = ({ title, date }: { title: string; date: string }) => {
  const { colors, space, borderRadius } = useTheme();
  return (
    <Column gap={space[2]}>
      <H6 fontWeight="bold" color={colors.secondary}>
        {title}
      </H6>
      <Body color={colors.palette.fwdGreyDarker}>{date}</Body>
    </Column>
  );
};

export default Title;
