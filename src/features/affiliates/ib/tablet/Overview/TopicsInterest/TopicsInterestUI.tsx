import { useTheme } from '@emotion/react';
import { FlashList } from '@shopify/flash-list';
import { Box, Row, Typography } from 'cube-ui-components';
import { EmptyRecord } from 'features/affiliates/components/EmptyRecord';
import TabButton from 'features/affiliates/components/TabButton';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import {
  LeadInterestedTopics,
  TopicInterestedByLead,
} from 'types/affiliateDashboard';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import BarChartContainer from './RoundedBarChartContainer';

export type TransformedData = {
  topicName: string;
  leadNum: number;
  viewTimes: number;
  categoryId: number;
  color: string;
};

const colorMapping: Record<number, string> = {
  1: '#391900',
  2: '#B74701',
  3: '#FF6816',
  4: '#FF9B0A',
  5: '#FED141',
  6: '#6ECEB2',
  7: '#B6E6D8',
  8: '#0097A9',
};
interface TopicsInterestUIProps {
  responseData?: TopicInterestedByLead;
}

const TopicsInterestUI: React.FC<TopicsInterestUIProps> = ({
  responseData,
}) => {
  const { colors, space, sizes, borderRadius } = useTheme();
  const [isDayTabActive, setIsDayTabActive] = useState<boolean>(true);
  const [isYearTabActive, setIsYearTabActive] = useState<boolean>(false);
  const [isIntegretedData, setIsIntegretedData] = useState<TransformedData[]>();
  const isEmptyData =
    !responseData?.past30Days ||
    responseData?.past30Days?.length === 0 ||
    !responseData?.thisYear ||
    responseData?.thisYear?.length === 0;

  const { t } = useTranslation(['affiliates']);

  function sortedDataByLeadsAndViewTimes(responseData: LeadInterestedTopics[]) {
    return (
      responseData &&
      [...responseData].sort((a, b) => {
        const leadComparison = b.leadNum - a.leadNum;
        const viewTimesComparison = b.viewTimes - a.viewTimes;
        const alphaComparison = a.topicName.localeCompare(
          b.topicName,
          undefined,
          { sensitivity: 'base' },
        );

        // Combine comparisons
        return leadComparison !== 0
          ? leadComparison
          : viewTimesComparison !== 0
          ? viewTimesComparison
          : alphaComparison;
      })
    );
  }

  function transformedData(responseData: LeadInterestedTopics[]) {
    const sortedData = sortedDataByLeadsAndViewTimes(responseData);

    // show max 8 topics
    const maxEightTopics = sortedData && sortedData.slice(0, 8);
    // mapping colors with each topic
    const transformedData: TransformedData[] =
      maxEightTopics &&
      maxEightTopics.map((item, index) => ({
        ...item,
        color: colorMapping[index + 1],
      }));
    setIsIntegretedData(transformedData);
  }

  useEffect(() => {
    if (responseData?.past30Days == null || responseData?.thisYear == null) {
      setIsDayTabActive(true);
      setIsYearTabActive(false);
    } else {
      responseData?.past30Days && transformedData(responseData?.past30Days);
    }
  }, []);

  const renderItem = ({ item }: { item: TransformedData }) => {
    return (
      <Box
        flexDirection="row"
        marginBottom={space[4]}
        justifyContent="space-between"
        gap={space[6]}>
        <Row style={{ flex: 1, alignItems: 'center' }} gap={space[2]}>
          <View
            style={{
              justifyContent: 'center',
              overflow: 'scroll',
            }}>
            <Box
              boxSize={space[3]}
              backgroundColor={item.color}
              borderRadius={1}
            />
          </View>

          <Typography.Body numberOfLines={2}>{item.topicName}</Typography.Body>
        </Row>

        <Row gap={space[2]} flex={1} alignItems="center">
          <Typography.Body style={{ flex: 1 }}>
            {numberToThousandsFormat(item.leadNum)}
          </Typography.Body>
          <Typography.Body style={{ flex: 1 }}>
            {numberToThousandsFormat(item.viewTimes)}
          </Typography.Body>
        </Row>
      </Box>
    );
  };

  return (
    <Box
      padding={space[5]}
      borderRadius={space[4]}
      backgroundColor={colors.background}
      gap={space[3]}>
      {/* Data analysis title */}
      <Row marginBottom={space[1]}>
        <Typography.H7 fontWeight="bold" color={colors.secondary}>
          {t('affiliates:affiliates.topicsYourLeadsInterestedTo')}
        </Typography.H7>
      </Row>

      {/* Tabs */}
      <Row gap={space[1]} marginBottom={space[4]}>
        <TabButton
          onPress={() => {
            setIsDayTabActive(true);
            setIsYearTabActive(false);
            if (responseData?.past30Days != null) {
              transformedData(responseData.past30Days);
            }
          }}
          isActive={isDayTabActive}
          label={t('affiliates:affiliates.past30Days')}
        />
        <TabButton
          onPress={() => {
            setIsDayTabActive(false);
            setIsYearTabActive(true);
            if (responseData?.thisYear != null) {
              transformedData(responseData.thisYear);
            }
          }}
          isActive={isYearTabActive}
          label={t('affiliates:affiliates.thisYear')}
        />
      </Row>

      {/* Bar Chart */}
      {isEmptyData ? (
        <Box display="flex" alignItems="center">
          <EmptyRecord />
          <TouchableOpacity
            style={{
              paddingHorizontal: space[4],
              paddingVertical: 10,
              borderColor: colors.palette.fwdOrange[100],
              borderWidth: 2,
              borderRadius: borderRadius['x-small'],
              marginTop: space[4],
              alignItems: 'center',
            }}>
            <Typography.LargeLabel
              color={colors.palette.fwdOrange[100]}
              fontWeight="bold">
              Make a post
            </Typography.LargeLabel>
          </TouchableOpacity>
        </Box>
      ) : (
        <>
          <View
            style={{
              paddingLeft: space[7],
              paddingRight: space[7],
            }}>
            {isIntegretedData && (
              <BarChartContainer dataset={isIntegretedData} />
            )}
          </View>

          <View style={{ alignItems: 'center' }}>
            <Typography.SmallLabel>
              {t('affiliates:affiliates.noOfLeadGenerated')}
            </Typography.SmallLabel>
          </View>
          {/* shown data */}
          <Box
            marginTop={space[4]}
            flexDirection="row"
            justifyContent="space-between"
            gap={space[6]}>
            <Row style={{ flex: 1 }}>
              <Typography.SmallLabel>
                {t('affiliates:affiliates.topic')}
              </Typography.SmallLabel>
            </Row>

            <Row gap={space[2]} style={{ flex: 1 }}>
              <Typography.SmallLabel style={{ flex: 1 }}>
                {t('affiliates:affiliates.leadGenerated')}
              </Typography.SmallLabel>
              <Typography.SmallLabel
                color={colors.palette.fwdDarkGreen[100]}
                style={{ flex: 1 }}>
                {t('affiliates:affiliates.noOfClick')}
              </Typography.SmallLabel>
            </Row>
          </Box>
          <View style={{ flex: 1 }}>
            <FlashList
              estimatedItemSize={100}
              renderItem={renderItem}
              data={isIntegretedData}
            />
          </View>
        </>
      )}
    </Box>
  );
};

export default TopicsInterestUI;
