import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  <PERSON><PERSON>,
  Column,
  H6,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Row,
  Switch,
  TextField,
} from 'cube-ui-components';
import { TFunc<PERSON>ey } from 'i18next';
import React from 'react';
import {
  Control,
  FieldErrors,
  UseFormSetValue,
  UseFormTrigger,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Nationality, Occupation, Religion } from 'types/optionList';

import { dateFormatUtil } from 'utils/helper/formatUtil';

import NameField from 'components/NameField';
import { MyInsuredFormValues } from 'features/coverageDetails/validation/common/insuredSchema';

import Autocomplete from 'components/Autocomplete';
import AutocompletePopup from 'components/AutocompletePopup';
import DatePickerCalendar from 'components/DatePickerCalendar';
import useCoverageForm from 'features/coverageDetails/hooks/common/useCoverageForm';
import { View } from 'react-native';
import { Gender } from 'types/person';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import InputContainer from '../../common/InputContainer.tablet';

export interface PolicyOwnderFormProps {
  control: Control<MyInsuredFormValues>;
  errors: FieldErrors<MyInsuredFormValues>;
  setValue: UseFormSetValue<MyInsuredFormValues>;
  setOwnerIsInsured?: (v: boolean) => void;
  onSearchLeadButtonPress?: () => void;
  ownerIsInsured?: boolean;
  ownerGender?: Gender;
  isEntity: boolean;
  trigger?: UseFormTrigger<MyInsuredFormValues>;
}

export default function InsuredForm({
  ownerIsInsured,
  setOwnerIsInsured,
  setValue,
  onSearchLeadButtonPress,
  control,
  errors,
  ownerGender,
  isEntity,
  trigger,
}: PolicyOwnderFormProps) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['coverageDetails']);

  const { defaultDate: dateOfBirthDefaultDate } = getDateOfBirthDropdownProps();

  const {
    age,
    gender: genderCoverageForm,
    smokingHabit: smokingHabitCoverageForm,
    occupation: occupationCoverageForm,
    relationship: relationshipCoverageForm,
    religion: religionCoverageForm,
    nationality: nationalityCoverageForm,
    country: countryCoverageForm,

    isFetchingOptionList,
  } = useCoverageForm<'my'>({
    ownerGender,
    setValue,
    control,
    trigger,
  });

  const header = isEntity ? (
    <Row justifyContent="space-between">
      <Row gap={space[2]} alignItems="center">
        <PictogramIcon.Account size={sizes[10]} />
        <H6 fontWeight="bold">
          {t('coverageDetails:formFields.keyman.title')}
        </H6>
      </Row>

      <Button
        variant="secondary"
        text={t('coverageDetails:existingLead')}
        textStyle={{ paddingHorizontal: space[6] }}
        onPress={onSearchLeadButtonPress}
      />
    </Row>
  ) : (
    <Row mt={6} mb={space[2]} alignItems="center">
      <PictogramIcon.Account size={sizes[10]} />
      <H6
        color={colors.secondary}
        fontWeight="bold"
        style={{ marginHorizontal: space[2] }}>
        {t('coverageDetails:formFields.ownerIsPersonCovered')}
      </H6>

      <Switch
        value={ownerIsInsured}
        onChange={value => setOwnerIsInsured?.(value)}
      />
    </Row>
  );

  const shouldShowForm = !ownerIsInsured || isEntity;

  return (
    <>
      {header}

      {shouldShowForm && (
        <View>
          {!isEntity && (
            <InputContainer>
              <Input
                control={control}
                as={Picker}
                name="relationship"
                label={t('coverageDetails:relationship')}
                items={relationshipCoverageForm?.options ?? []}
                disabled={isFetchingOptionList}
                type="chip"
                error={t(errors.relationship?.message as TFuncKey)}
              />
            </InputContainer>
          )}

          <Row flexWrap="wrap" w="100%">
            <InputContainer>
              <Input
                style={{ flex: 1 }}
                control={control}
                as={NameField}
                name="firstName"
                label={t('coverageDetails:formFields.firstName')}
                error={t(errors?.firstName?.message as TFuncKey)}
              />
            </InputContainer>
            <InputContainer>
              <Row alignItems="flex-start" gap={space[3]}>
                <Column flex={3}>
                  <Input
                    control={control}
                    as={DatePickerCalendar}
                    name="dob"
                    label={t('coverageDetails:formFields.dateOfBirth')}
                    hint="DD/MM/YYYY"
                    maxDate={new Date()}
                    formatDate={val => (val ? dateFormatUtil(val) : '')}
                    defaultDate={dateOfBirthDefaultDate}
                  />
                </Column>
                <Column flex={1}>
                  <TextField
                    value={age}
                    disabled
                    label={t('coverageDetails:formFields.age')}
                  />
                </Column>
              </Row>
            </InputContainer>

            <InputContainer>
              <Input
                style={{ flex: 1 }}
                control={control}
                as={Picker}
                disabled={genderCoverageForm?.disabled}
                label={t('coverageDetails:gender')}
                name="gender"
                type="text"
                items={genderCoverageForm?.options}
                error={t(errors?.gender?.message as TFuncKey)}
              />
            </InputContainer>

            <InputContainer>
              <Input
                style={{ flex: 1 }}
                control={control}
                as={Picker}
                name="smokingHabit"
                type="text"
                label={t('coverageDetails:formFields.smokingHabit')}
                error={t(errors?.smokingHabit?.message as TFuncKey)}
                disabled={smokingHabitCoverageForm?.disabled}
                items={smokingHabitCoverageForm?.options}
              />
            </InputContainer>

            <InputContainer>
              <Input
                style={{ flex: 1 }}
                control={control}
                as={AutocompletePopup<Nationality, string>}
                name="nationality"
                label={t('coverageDetails:formFields.nationality')}
                disabled={isFetchingOptionList}
                data={nationalityCoverageForm?.options}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
                searchable
              />
            </InputContainer>

            <InputContainer>
              <Input
                style={{ flex: 1 }}
                control={control}
                as={Autocomplete<Religion, string>}
                name="religion"
                label={t('coverageDetails:formFields.religion')}
                disabled={isFetchingOptionList}
                data={religionCoverageForm?.options}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
              />
            </InputContainer>

            <InputContainer>
              <Input
                style={{ flex: 1 }}
                control={control}
                as={AutocompletePopup<Occupation, string>}
                name="occupation"
                label={t('coverageDetails:formFields.occupation')}
                disabled={
                  isFetchingOptionList || occupationCoverageForm?.disabled
                }
                data={occupationCoverageForm?.options}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
                hint={occupationCoverageForm?.occupationClass}
                onChange={occupationCoverageForm?.onChange}
                searchable
              />
            </InputContainer>
          </Row>
        </View>
      )}
    </>
  );
}
