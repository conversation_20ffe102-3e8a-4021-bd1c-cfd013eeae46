import { useTheme } from '@emotion/react';
import DatePickerCalendar from 'components/DatePickerCalendar';
import Input from 'components/Input';
import {
  Icon,
  Label,
  Picker,
  PictogramIcon,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import React from 'react';
import {
  Control,
  FieldErrors,
  UseFormGetValues,
  UseFormSetValue,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Country,
  CountryCode,
  Nationality,
  Occupation,
  Religion,
} from 'types/optionList';
import { dateFormatUtil } from 'utils/helper/formatUtil';

import NameField from 'components/NameField';

import Autocomplete from 'components/Autocomplete';
import { Pressable, View } from 'react-native';

import AutocompletePopup from 'components/AutocompletePopup';
import PhoneField from 'components/PhoneField';
import { getCountryCodeDisplayedLabel } from 'constants/optionList';
import useCoverageForm from 'features/coverageDetails/hooks/common/useCoverageForm';
import { MAX_PHONE_LENGTH } from 'features/coverageDetails/validation/common/constant';
import { IdOwnerFormValues } from 'features/coverageDetails/validation/common/ownerSchema';
import { TFuncKey } from 'i18next';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import InputContainer from '../../common/InputContainer.tablet';

export interface PolicyOwnerFormProps {
  control: Control<IdOwnerFormValues>;
  errors: FieldErrors<IdOwnerFormValues>;
  setValue: UseFormSetValue<IdOwnerFormValues>;
  getValues: UseFormGetValues<IdOwnerFormValues>;
  onSearchLeadButtonPress?: () => void;
}

export default function PolicyOwnerForm({
  control,
  errors,
  setValue,
  onSearchLeadButtonPress,
}: PolicyOwnerFormProps) {
  const { space, colors, sizes, borderRadius } = useTheme();

  const { t } = useTranslation(['coverageDetails']);

  const { defaultDate: dateOfBirthDefaultDate } = getDateOfBirthDropdownProps();

  const {
    age,
    occupation: occupationCoverageForm,
    gender: genderCoverageForm,
    religion: religionCoverageForm,
    country: countryCoverageForm,
    countryCode: countryCodeCoverageForm,
    nationality: nationalityCoverageForm,
    isFetchingOptionList,
  } = useCoverageForm<'id'>({
    setValue,
    control,
  });

  return (
    <>
      <Row
        alignItems="center"
        bgColor={colors.background}
        pt={space[6]}
        pb={space[5]}
        px={space[6]}
        gap={space[2]}
        width="100%"
        borderTopLeftRadius={space[4]}
        borderTopRightRadius={space[4]}
        justifyContent="space-between">
        <Row gap={space[2]} alignItems="center">
          <PictogramIcon.Account size={sizes[10]} />
          <Typography.H6 fontWeight="bold">
            {t('coverageDetails:formFields.title')}
          </Typography.H6>
        </Row>

        {onSearchLeadButtonPress && (
          <Pressable onPress={onSearchLeadButtonPress}>
            <Row alignItems="center" gap={space[1]}>
              <Icon.Search
                size={sizes[4]}
                fill={colors.palette.fwdAlternativeOrange[100]}
              />
              <Label
                color={colors.palette.fwdAlternativeOrange[100]}
                fontWeight="medium">
                {t('coverageDetails:existingLead')}
              </Label>
            </Row>
          </Pressable>
        )}
      </Row>

      <View
        style={{
          paddingLeft: space[6],
          paddingRight: space[1],
          backgroundColor: colors.background,
          borderBottomLeftRadius: borderRadius.medium,
          borderBottomRightRadius: borderRadius.medium,
        }}>
        <Row flexWrap="wrap" w="100%">
          <InputContainer>
            <Input
              style={{ flex: 1 }}
              control={control}
              as={NameField}
              name="firstName"
              label={t('coverageDetails:formFields.firstName')}
              error={t((errors?.firstName?.message as TFuncKey) || '')}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
          </InputContainer>

          <InputContainer>
            <Input
              style={{
                flex: 3,
                marginRight: space[3],
              }}
              control={control}
              as={DatePickerCalendar}
              name="dob"
              label={t('coverageDetails:formFields.dateOfBirth')}
              hint="DD/MM/YYYY"
              maxDate={new Date()}
              formatDate={val => (val ? dateFormatUtil(val) : '')}
              defaultDate={dateOfBirthDefaultDate}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
            <TextField
              style={{
                flex: 1,
              }}
              value={age}
              disabled
              label={t('coverageDetails:formFields.age')}
            />
          </InputContainer>

          <InputContainer>
            <Input
              style={{ flex: 1 }}
              control={control}
              as={Picker}
              name="gender"
              type="text"
              error={t((errors?.gender?.message as TFuncKey) || '')}
              items={genderCoverageForm?.options}
              label={t('coverageDetails:gender')}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
          </InputContainer>

          <InputContainer>
            <Input
              style={{ flex: 1 }}
              control={control}
              as={AutocompletePopup<Nationality, string>}
              name="nationality"
              label={t('coverageDetails:formFields.nationality')}
              disabled={isFetchingOptionList}
              data={nationalityCoverageForm?.options}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              searchable
            />
          </InputContainer>

          <InputContainer>
            <Input
              style={{ flex: 1 }}
              control={control}
              as={Autocomplete<Religion, string>}
              name="religion"
              label={t('coverageDetails:formFields.religion')}
              disabled={isFetchingOptionList}
              data={religionCoverageForm?.options}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
          </InputContainer>

          <InputContainer>
            <Input
              style={{ flex: 1 }}
              control={control}
              as={AutocompletePopup<Country, string>}
              name="residence"
              label={t('coverageDetails:formFields.residence')}
              disabled={isFetchingOptionList}
              data={countryCoverageForm?.options}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              searchable
            />
          </InputContainer>

          <InputContainer>
            <Input
              style={{ flex: 1 }}
              control={control}
              as={AutocompletePopup<Occupation, string>}
              name="occupation"
              label={t('coverageDetails:formFields.occupation')}
              disabled={isFetchingOptionList}
              data={occupationCoverageForm?.options}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              hint={occupationCoverageForm?.occupationClass}
              onChange={occupationCoverageForm.onChange}
              searchable
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
          </InputContainer>

          <InputContainer>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="code"
              data={countryCodeCoverageForm.options}
              getItemValue={item => item.value}
              getItemLabel={item => item.label}
              keyExtractor={item => item.value}
              getDisplayedLabel={item => getCountryCodeDisplayedLabel(item)}
              disabled={countryCodeCoverageForm.disabled}
              label={t('coverageDetails:formFields.phoneCode')}
              style={{ flex: 1, width: space[27], marginRight: space[3] }}
              modalTitle={t('coverageDetails:formFields.countryCode')}
              searchLabel={t('coverageDetails:formFields.searchCountryCode')}
              searchable
            />
            <Input
              style={{ flex: 3 }}
              control={control}
              as={PhoneField}
              name="phoneMobile"
              maxLength={MAX_PHONE_LENGTH}
              label={t('coverageDetails:formFields.phoneNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              size={'large'}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
          </InputContainer>
        </Row>
      </View>
    </>
  );
}
