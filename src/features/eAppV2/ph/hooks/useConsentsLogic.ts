import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetCase } from 'hooks/useGetCase';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import useToggle from 'hooks/useToggle';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useCallback } from 'react';
import { UseFormHandleSubmit } from 'react-hook-form';
import GATracking from 'utils/helper/gaTracking';
import { toApplicationConsent } from '../utils/caseUtils';
import {
  ExistingPoliciesForm,
  ExistingPolicyForm,
} from '../validations/consentsValidation';

const useConsentsLogic = ({
  handleSubmit,
}: {
  handleSubmit: UseFormHandleSubmit<ExistingPoliciesForm>;
}) => {
  const [dataPrivacyModalVisible, showDataPrivacyModal, hideDataPrivacyModal] =
    useToggle();
  const [
    dataPrivacyReminderVisible,
    showDataPrivacyReminder,
    hideDataPrivacyReminder,
  ] = useToggle();

  const consentsAnswers = useEAppStore(state => state.consentsAnswers);
  const updateConsentsDeclarationAnswers = useEAppStore(
    state => state.updateConsentsDeclarationAnswers,
  );
  const updateExistingPolicyData = useEAppStore(
    state => state.updateExistingPolicyData,
  );
  const existingPolicyData = useEAppStore(state => state.existingPolicyData);
  const keyboardShown = useKeyboardShown();

  const isActionDisabled =
    consentsAnswers.declaration.hasExistingInsuranceInForce === null ||
    consentsAnswers.declaration.hasLapsedPolicyToReinstate === null ||
    consentsAnswers.declaration.willPayPremiumsByLoanOrSurrender === null ||
    consentsAnswers.declaration.willReplaceLifeInsurance === null ||
    !consentsAnswers.declaration.confirmCheck;

  const shouldShowExistedPolicy =
    consentsAnswers.declaration.hasExistingInsuranceInForce ||
    consentsAnswers.declaration.hasLapsedPolicyToReinstate ||
    consentsAnswers.declaration.willPayPremiumsByLoanOrSurrender ||
    consentsAnswers.declaration.willReplaceLifeInsurance;

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const onSubmit = useCallback(() => {
    GATracking.logCustomEvent('application', {
      action_type: 'eapp_submit_consents',
      application_type: 'F2F',
    });

    if (shouldShowExistedPolicy) {
      handleSubmit(data => {
        updateExistingPolicyData(data.policies as Array<ExistingPolicyForm>);
        showDataPrivacyModal();
      })();
    } else {
      showDataPrivacyModal();
    }
  }, [
    handleSubmit,
    shouldShowExistedPolicy,
    showDataPrivacyModal,
    updateExistingPolicyData,
  ]);

  const agentId = useBoundStore(state => state.auth.agentCode);
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj, isFetching: isGettingCase } = useGetCase(caseId ?? '');
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();

  const onSave = useCallback(async () => {
    if (agentId && caseId && caseObj) {
      await saveApplication({
        caseId,
        data: {
          ...caseObj.application,
          ...toApplicationConsent(consentsAnswers, existingPolicyData, agentId),
        },
      });
    }
  }, [
    agentId,
    caseId,
    caseObj,
    consentsAnswers,
    existingPolicyData,
    saveApplication,
  ]);

  return {
    dataPrivacyModalVisible,
    showDataPrivacyModal,
    hideDataPrivacyModal,
    dataPrivacyReminderVisible,
    showDataPrivacyReminder,
    hideDataPrivacyReminder,
    consentsAnswers,
    updateConsentsDeclarationAnswers,
    updateExistingPolicyData,
    keyboardShown,
    isActionDisabled,
    existingPolicyData,
    shouldShowExistedPolicy,
    isNarrowScreen,
    onSubmit,
    onSave,
  };
};

export default useConsentsLogic;

export type ConsentsLogic = ReturnType<typeof useConsentsLogic>;
