import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Checkbox, LargeBody, LargeLabel, Row } from 'cube-ui-components';
import ScreenHeaderTablet from 'navigation/components/ScreenHeader/tablet';
import ScreenHeaderPhone from 'navigation/components/ScreenHeader/phone';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { PdpaNotice } from '../../../../../common/components/pdpaNotice/PdpaNotice';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { ID_PDPA_LINK } from 'features/eAppV2/id/constants/consent';
import { StyleSheet } from 'react-native';
import useToggle from 'hooks/useToggle';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const listOfIndexes = [1, 2, 3, 4, 5] as const;

export default function PdpReview() {
  const { borderRadius, colors, space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { caseObj } = useGetActiveCase();
  const pdpChecked = caseObj?.application?.proposerConsent?.[0]?.pdpCheck;

  const [pdpaVisible, showPdpaNotice, hidePdpaNotice] = useToggle();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const ScreenHeader = isTabletMode ? ScreenHeaderTablet : ScreenHeaderPhone;

  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeader
        customTitle={t('eApp:review.declaration.pdp')}
        route={'PdpReview'}
        isLeftCrossBackShown={isTabletMode}
        isLeftArrowBackShown={!isTabletMode}
      />
      <ScrollViewContainer>
        <Box
          borderRadius={borderRadius.large}
          backgroundColor={colors.background}
          pb={space[10]}>
          {listOfIndexes.map(index => {
            const indexWithFormat = `${index}.`;
            return (
              <Row mb={space[5]} gap={space[2]}>
                <LargeBody>{indexWithFormat}</LargeBody>
                <PdpText children={t(`eApp:pdp.${index}`)} />
              </Row>
            );
          })}
          <LargeBody>
            {t('eApp:pdp.agreement.1')}
            <HyperLink
              fontWeight="bold"
              suppressHighlighting
              onPress={showPdpaNotice}
              color={colors.primary}>
              {t('eApp:pdp.notice')}
            </HyperLink>
            {t('eApp:pdp.agreement.2')}
          </LargeBody>
          <Box gap={space[3]} mt={space[5]}>
            <LargeLabel fontWeight="bold">{t('eApp:pdp.marketing')}</LargeLabel>
            <LargeBody>{t('eApp:pdp.marketing.description')}</LargeBody>
            <Row
              borderRadius={borderRadius['x-small']}
              borderWidth={1}
              borderColor={colors.palette.fwdGrey[100]}
              backgroundColor={colors.palette.fwdGrey[20]}
              padding={space[3]}
              marginTop={space[2]}>
              <Box marginRight={space[2]} flex={1}>
                <Checkbox
                  value={pdpChecked}
                  label={t('eApp:pdp.marketing.checkbox')}
                  labelStyle={styles.checkboxLabel}
                  style={styles.checkbox}
                  disabled
                />
              </Box>
            </Row>
          </Box>
          <PdpaNotice
            url={ID_PDPA_LINK}
            visible={pdpaVisible}
            onDismiss={hidePdpaNotice}
            onAgree={hidePdpaNotice}
          />
        </Box>
      </ScrollViewContainer>
    </Box>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    padding: space[6],
    marginBottom: space[6],
  }),
);

const HyperLink = styled(LargeBody)({
  textDecorationLine: 'underline',
});

const PdpText = styled(LargeBody)({
  flex: 1,
});

const styles = StyleSheet.create({
  checkbox: {
    alignItems: 'flex-start',
    marginTop: 4,
  },
  checkboxLabel: {
    flex: 1,
  },
});
