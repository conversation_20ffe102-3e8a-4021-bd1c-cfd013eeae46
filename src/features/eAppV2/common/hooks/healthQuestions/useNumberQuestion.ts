import { NumberHealthQuestion } from 'types/healthQuestion';
import { useController } from 'react-hook-form';

type Props = {
  question: NumberHealthQuestion;
};

type UseNumberQuestion = {
  text: string;
  onChange: (text: string) => void;
  onBlur: () => void;
};

const useNumberQuestion = ({ question }: Props): UseNumberQuestion => {
  const { field } = useController({
    name: question.path,
  });
  const answer = field.value || [];

  const onChange = (text: string) => field.onChange([text]);

  //   TODO: onBlur should call update answer of enquiry
  const onBlur = () => field.onBlur();

  return {
    text: answer?.[0],
    onChange,
    onBlur,
  };
};

export default useNumberQuestion;
