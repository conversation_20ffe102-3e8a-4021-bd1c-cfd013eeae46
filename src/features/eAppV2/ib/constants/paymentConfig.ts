// payment gateway
export const GATEWAY_EXPIRED_DAYS = '3';
export const GATEWAY_AGENCY_CREDIT = 'EGHL-AG-CC';
export const GATEWAY_BANCA_CREDIT = 'EGHL-BC-CC';
export const GATEWAY_AGENCY_FPX = 'EGHL-AG-FPX';
export const GATEWAY_BANCA_FPX = 'EGHL-BC-FPX';
export const GATEWAY_AGENCY_EWALLET = 'IPAY88-FWDIB-WA-AG';
export const GATEWAY_BANCA_EWALLET = 'IPAY88-FWDIB-WA-BC';
export const GATEWAY_AGENCY_EPP = 'IPAY88-FWDIB-EPP-AG';
export const GATEWAY_BANCA_EPP = 'IPAY88-FWDIB-EPP-BC';

// eMandate
export const EMANDATE_APP_TYPE = '01';
export const EMANDATE_MAX_FREQ = '4';
export const EMANDATE_FREQ_MODE = 'DL';
export const EMANDATE_GATEWAY_CODE = 'GBSN_EMANDATE_AG_FPX_AD';

// payment slip
export const PAYMENT_SLIP_BANK_NAME = 'Hong Leong Bank';
export const PAYMENT_SLIP_BANK_ACCOUNT = '***********';
