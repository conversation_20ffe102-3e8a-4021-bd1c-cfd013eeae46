import { requiredMessage } from 'features/eAppV2/common/constants/eAppErrorMessages';
import {
  normalizedSpaceString,
  nullableDate,
} from 'features/eAppV2/common/validations/eAppCommonSchema';
import { mysIdNumber } from 'features/eAppV2/my/validations/commonSchema';
import { Application } from 'types/case';
import { Party } from 'types/party';
import {
  Gender,
  MaritalStatus,
  Registration,
  Religion,
  SmokingHabit,
} from 'types/person';
import { calculateAge } from 'utils/helper/calculateAge';
import { parsePartyDob, toPartyDob } from 'utils/helper/dateUtil';
import { array, InferType, object, ref, string } from 'yup';
import { ibName } from '../../commonSchema';

export const mainPartyPersonalDetailsSchema = object({
  id: string(),
  relationship: string(),
  fullName: ibName().required(requiredMessage),
  gender: string().required(requiredMessage),
  dob: nullableDate(),
  smokingHabit: string().required(requiredMessage),
  source: string().when('$isEntity', {
    is: true,
    then: schema => schema.optional(),
    otherwise: schema => schema.required(requiredMessage),
  }),
  title: string().required(requiredMessage),
  primaryIdType: string().required(requiredMessage),
  primaryId: mysIdNumber('primaryIdType', 'dob', 'gender').required(
    requiredMessage,
  ),
  religion: string().required(requiredMessage),
  race: string().required(requiredMessage),
  maritalStatus: string().required(requiredMessage),
  campaignCode: string(),
  personalDetailsRole: string().oneOf(['owner', 'insured']),
  secondaryAgentId: string(),
  sourcesOfWealth: array()
    .of(string().required(requiredMessage))
    .when('personalDetailsRole', {
      is: 'owner',
      then: schema =>
        schema
          .required(requiredMessage)
          .test(
            'min-required',
            requiredMessage,
            value => Array.isArray(value) && value.length > 0,
          ),
    }),
  sourcesOfFund: array()
    .of(string().required(requiredMessage))
    .when('personalDetailsRole', {
      is: 'owner',
      then: schema =>
        schema
          .required(requiredMessage)
          .test(
            'min-required',
            requiredMessage,
            value => Array.isArray(value) && value.length > 0,
          ),
    }),
  additionalIdType: string(),
  additionalId: normalizedSpaceString().test(
    'required-additional-id',
    requiredMessage,
    (value, ctx) => {
      const additionalIdType = ctx.resolve(ref('additionalIdType'));
      if (additionalIdType) return Boolean(value);
      return true;
    },
  ),
});

export type MainPartyPersonalDetailsForm = InferType<
  typeof mainPartyPersonalDetailsSchema
>;

export const mainPartyPersonalDetailsDefaultValue: MainPartyPersonalDetailsForm =
  {
    id: '',
    relationship: '',
    fullName: '',
    gender: '',
    dob: null,
    smokingHabit: '',
    source: '',
    title: '',
    primaryIdType: '',
    primaryId: '',
    religion: '',
    race: '',
    maritalStatus: '',
    campaignCode: '',
    additionalIdType: '',
    additionalId: '',
  };

export const partyToMainPartyPersonalDetails = (
  party?: Party,
  application?: Application,
): MainPartyPersonalDetailsForm => {
  if (!party) return mainPartyPersonalDetailsDefaultValue;
  const primaryId = party.person?.registrations?.find(r => r.type === 'DEFAULT');
  const additionalId = party.person?.registrations?.find(
    r => r.type === 'ADDITIONAL',
  );
  return {
    id: party.id || '',
    relationship: party.relationship || '',
    fullName: party.person?.name.firstName || '',
    gender: party.person?.gender || '',
    dob: party.person?.dateOfBirth
      ? parsePartyDob(party.person?.dateOfBirth)
      : null,
    smokingHabit: party.person?.isSmoker
      ? SmokingHabit.SMOKER
      : SmokingHabit.NONSMOKER,
    source: party.sourceLeadId || '',
    title: party.person?.name.title || '',
    primaryIdType: primaryId?.idType || '',
    primaryId: primaryId?.id || '',
    religion: party.person?.religion || '',
    race: party.person?.ethnicityCode || '',
    maritalStatus: party.person?.maritalStatus || '',
    campaignCode: party.referralInfo?.referralCampaignCode || '',
    secondaryAgentId: application?.secondaryAgent?.agentCode || '',
    sourcesOfFund: party.person?.sourcesOfFund || [],
    sourcesOfWealth: party.person?.sourcesOfWealth || [],
    additionalIdType: additionalId?.idType || '',
    additionalId: additionalId?.id || '',
  };
};

export const mainPartyPersonalDetailsToParty = (
  form: MainPartyPersonalDetailsForm,
): Pick<
  Party,
  'id' | 'relationship' | 'person' | 'referralInfo' | 'sourceLeadId'
> => {
  return {
    id: form.id || '',
    relationship: form.relationship,
    person: {
      name: {
        title: form.title,
        firstName: form.fullName,
      },
      dateOfBirth: toPartyDob(form.dob),
      age: form.dob ? calculateAge(form.dob) : 0,
      gender: form.gender as Gender,
      isSmoker: form.smokingHabit === SmokingHabit.SMOKER,
      religion: form.religion as Religion,
      ethnicityCode: form.race,
      maritalStatus: form.maritalStatus as MaritalStatus,
      registrations: [
        {
          type: 'DEFAULT',
          idType: form.primaryIdType,
          id: form.primaryId,
        },
        form.additionalIdType && {
          type: 'ADDITIONAL',
          idType: form.additionalIdType,
          id: form.additionalId,
        },
      ].filter((r): r is Registration => Boolean(r)),
      sourcesOfFund: form.sourcesOfFund,
      sourcesOfWealth: form.sourcesOfWealth,
    },
    referralInfo: {
      referralCampaignCode: form.campaignCode,
    },
    sourceLeadId: form.source,
  };
};
