import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  H6,
  H7,
  <PERSON><PERSON><PERSON>,
  Picker,
  PictogramIcon,
  Row,
} from 'cube-ui-components';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useAlert } from 'hooks/useAlert';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, ScrollView } from 'react-native';

export default function ChargePremiumAuthorizationPhone() {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const next = useEAppProgressBarStore(state => state.next);
  const { data: optionList } = useGetOptionList();
  const { disabledForm } = useDisabledEAppForm();
  const { caseObj } = useGetActiveCase();
  const [financialKnowledgeLevel, setFinancialKnowledgeLevel] = useState(
    () => caseObj?.application?.proposerConsent?.[0].authLevel || '',
  );

  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const { alertError } = useAlert();

  const onNext = useCallback(async () => {
    if (!caseObj) return;
    saveApplication({
      caseId: caseObj.id,
      data: {
        ...caseObj.application,
        proposerConsent: (caseObj.application?.proposerConsent || []).map(
          consent => ({
            ...consent,
            authLevel: financialKnowledgeLevel,
          }),
        ),
      },
    })
      .then(() => next())
      .catch(() => alertError(t('eApp:failedToSaveData')));
  }, [alertError, caseObj, financialKnowledgeLevel, next, saveApplication, t]);

  return (
    <Box flex={1}>
      <ScrollView contentContainerStyle={{ paddingBottom: space[4] }}>
        <Box mx={space[4]} mt={space[4]} mb={space[3]}>
          <H6 fontWeight="bold">{t('eApp:changePremiumAuthorization')}</H6>
        </Box>
        <Container>
          <Box gap={space[1]}>
            <Row mb={space[3]} gap={space[1]} alignItems="center">
              <PictogramIcon.InformationDocument size={40} />
              <H7 fontWeight="bold">{t('eApp:changePremiumAuthorization')}</H7>
            </Row>
            <H7 fontWeight="bold">
              {t(
                'eApp:changePremiumAuthorization.financialPlanningAndKnowledge.question',
              )}
            </H7>
            <LevelPicker
              type="chip"
              size="large"
              items={optionList?.AUTH_LEVEL?.options || []}
              value={financialKnowledgeLevel}
              onChange={setFinancialKnowledgeLevel}
              highlight={Platform.OS === 'ios' && !financialKnowledgeLevel}
            />
          </Box>
        </Container>
        <Box height={space[3]} />
        <Container>
          <Row mb={space[3]} gap={space[1]} alignItems="center">
            <PictogramIcon.InformationDocument size={40} />
            <H7 fontWeight="bold">
              {t('eApp:changePremiumAuthorization.declaration')}
            </H7>
          </Row>
          <LargeBody>
            {t('eApp:changePremiumAuthorization.declaration.content')}
          </LargeBody>
        </Container>
      </ScrollView>
      <EAppFooterPhone
        progressLock="consents--authorizationToChargePremium"
        onPrimaryPress={onNext}
        primarySubLabel={t('eApp:bar.ePolicyAndENotices')}
        primaryLoading={isSavingApplication}
        primaryDisabled={!financialKnowledgeLevel || disabledForm}
        totalIncompleteRequiredFields={financialKnowledgeLevel ? 0 : 1}
      />
    </Box>
  );
}

const Container = styled.View(({ theme: { colors, space, borderRadius } }) => {
  return {
    borderRadius: borderRadius.large,
    padding: space[4],
    backgroundColor: colors.background,
    marginHorizontal: space[4],
    overflow: 'hidden',
  };
});

const LevelPicker = styled(Picker)(({ theme }) => ({
  marginTop: theme.space[4],
}));
