import React, {
  forwardRef,
  MutableRefObject,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import NominationFormPhone from './NominationForm.phone';
import NominationFormTablet from './NominationForm.tablet';
import {
  Control,
  UseFieldArrayUpdate,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
} from 'react-hook-form';
import { NominationInfoForm } from 'features/eAppV2/ib/validations/applicationDetails/nomination/nominationInfoValidation';
import { useOcr } from '../../../sections/ocr/useOcr';

export type NominationFormRef = { expand?: () => void };

interface Props {
  control: Control<NominationInfoForm>;
  index: number;
  onDelete?: () => void;
  isLatestItem: boolean;
  setValue: UseFormSetValue<NominationInfoForm>;
  getValues: UseFormGetValues<NominationInfoForm>;
  trigger: UseFormTrigger<NominationInfoForm>;
  update: UseFieldArrayUpdate<NominationInfoForm>;
  ocrCapture: MutableRefObject<(ReturnType<typeof useOcr>['ocrCapture']['current'])[]>
}

const NominationForm = forwardRef<NominationFormRef, Props>(
  (props: Props, ref: React.ForwardedRef<NominationFormRef>) => {
    const [isCollapsed, setIsCollapsed] = useState(true);

    useEffect(() => {
      setIsCollapsed(props.isLatestItem ? false : true);
    }, [props.isLatestItem]);

    const expandForm = () => {
      setIsCollapsed(false);
    };

    useImperativeHandle(ref, () => ({
      expand: expandForm,
    }));

    return (
      <DeviceBasedRendering
        tablet={
          <NominationFormTablet
            {...props}
            isCollapsed={isCollapsed}
            setIsCollapsed={setIsCollapsed}
          />
        }
        phone={
          <NominationFormPhone
            {...props}
            isCollapsed={isCollapsed}
            setIsCollapsed={setIsCollapsed}
            update={props.update}
          />
        }
      />
    );
  },
);

export default NominationForm;
