import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  CompositeNavigationProp,
  useFocusEffect,
  useNavigation,
} from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Box, Chip, Column, H7, Row } from 'cube-ui-components';
import { Icon } from 'cube-ui-components/';
import PolicySearch from 'features/policy/components/PolicySearch';
import useSearchPolicies from 'features/policy/hooks/useSearchPolicies';
import { useBottomBar } from 'hooks/useBottomBar';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetNewBusinessPolicyByAgentId } from 'hooks/useGetPolicyList';
import { useGetSavedProposals } from 'hooks/useGetSavedProposals';
import React, {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  Platform,
  ScrollView as ScrollViewIOS,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { CaseStatus } from 'types/case';
import { NewBusinessParamList, RootStackParamList } from 'types/navigation';
import { ReviewApplicationListItem } from './components/ReviewApplicationListItem';

import { FlashList } from '@shopify/flash-list';
import NBListCardSet, {
  AgentNewBusinessPolicyPHWithFieldsFromOWB,
} from 'features/policy/components/NewBusinessScreen/phone/NBListCardSet';
import {
  Line,
  TabButtonV2,
  TabContainer,
} from 'features/policy/components/phone/TabButton';
import { CaseStatusToNBSubmissionForReviewStatusMap } from 'features/policy/hooks/useGetCaseObjInfo';
import { casePaymentModeToPremiumMatcher } from 'features/policy/utils/policyDetailUtil';
import debounce from 'lodash/debounce';
import { ScrollView as ScrollViewAOS } from 'react-native-gesture-handler';
import { SortDirectionKeys } from 'types/eRecruit';
import { PartyRole } from 'types/party';
import {
  AllNewBusinessPolicyData,
  NBIssued,
  NBPending,
  NBPolicyLeaderReviewStatus,
  NBPolicyLeaderReviewSubmissionStatus,
  PoliciesToReviewApplicationPolicyList,
  PolicyStatus,
  TabConfig,
} from 'types/policy';
import { PaymentMode, SavedProposal } from 'types/proposal';
import { sortDateHandler } from 'utils/helper/dateUtil';
import SubmissionStatusActionPanel from './components/ActionPanel/SubmissionStatusActionPanel';
import CountAndSortHeader from './components/CountAndSortHeader';
import { Placeholder } from './components/PlaceHolder';
// import useSearchPolicies from 'features/policy/hooks/useSearchPoliciesV2';

const ICON_SIZE = 24; // sizes[6]
const PADDING_X = 16; // space[4] - Padding horizontal of the main container
const SMALL_ICON_SIZE = 18; // sizes[5]

type StatusType = {
  type: 'reviewApplication' | 'submission' | 'pending' | 'issued';
  value: string;
  label: string;
};

export default function NewBusinessScreen() {
  const { t } = useTranslation(['policy']);
  const theme = useTheme();
  const { colors, space, borderRadius, sizes } = theme;
  const lastScrollY = useSharedValue(0);
  const [showSearch, setShowSearch] = useState(false);
  const { showBottomBar, hideBottomBar } = useBottomBar();
  const ScrollView = Platform.OS === 'ios' ? ScrollViewIOS : ScrollViewAOS;

  // Store
  const loginAgentCode = useBoundStore(state => state.auth.agentCode);
  const { data: agentProfile } = useGetAgentProfile();

  /**
   * Show review application tab function
   */

  const isReviewApplicationTabVisible = agentProfile?.isApprover ?? false;
  // const isReviewApplicationTabVisible = true;

  // * Show chip filter for business status
  const COMMON_STATUSES: StatusType[] = [
    { type: 'submission', value: 'submission', label: 'Submission' },
    { type: 'pending', value: 'pending', label: 'In Progress' },
    { type: 'issued', value: 'issued', label: 'Issued' },
  ] as const;

  // * Check agent if has access to review application tab
  const BIZ_STATUS_CHIP_FILTER_CONFIG: StatusType[] =
    isReviewApplicationTabVisible
      ? ([
          {
            type: 'reviewApplication',
            value: 'reviewApplication',
            label: 'Review application',
          },
          ...COMMON_STATUSES,
        ] as const)
      : COMMON_STATUSES;

  // Idle
  const [disabled, setDisabled] = useState(true);
  const [filterVisible, setFilterVisible] = useState(false);

  // Sort
  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  // Filter
  const [openFilterPanel, setOpenFilterPanel] = useState(false);
  // CUBEFIB-3170: keith - always pick the chip with data != null, and if all chips have data, then start with the first chip from left to right [Review Application, Submission, Pending, Issued]. Otherwise, the first chip will be selected by default
  // const [filterBy, setFilterBy] = useState<string | null>();
  const [activeChip, setActiveChip] =
    useState<(typeof BIZ_STATUS_CHIP_FILTER_CONFIG)[number]['type']>();
  const [filterByReviewStatus, setFilteredByReviewStatus] = useState<
    NBPolicyLeaderReviewStatus[]
  >([]);
  const isFilteredByReviewStatus = filterByReviewStatus?.length > 0;

  // Navigation
  const navigation = useNavigation<
    CompositeNavigationProp<
      NativeStackNavigationProp<RootStackParamList, 'PoliciesNewBusiness'>, // refer to IbStackNavigator
      NativeStackNavigationProp<NewBusinessParamList, 'NBList'> // refer to PoliciesNewBusinessNavigator
    >
  >();

  // Submission status
  const [statusFilterParams, setsStatusFilterParams] = useState<CaseStatus[]>([
    CaseStatus.APP_SUBMITTED,
    CaseStatus.PENDING_FOR_LEADER,
    CaseStatus.EXPIRED_AFTER_15_DAYS,
    CaseStatus.REJECTED_BY_LEADER,
    CaseStatus.APPROVED_BY_LEADER,
  ]);

  // ***************************************** Data ***************************************** //
  /**
   * Leader review application
   */
  const {
    data: leaderReviewPolicyData,
    isLoading: isLeaderReviewPolicyLoading,
  } = useGetSavedProposals({
    status: statusFilterParams.filter(
      caseStatus => caseStatus !== CaseStatus.APP_SUBMITTED,
    ),
    sort_by: '+updatedAt',
    q: undefined,
    downlineOnly: true,
  });
  const leaderReviewPolicyList = useMemo(() => {
    if (!leaderReviewPolicyData) return [];
    return leaderReviewPolicyData?.pages?.map(page => page?.data)?.flat();
  }, [leaderReviewPolicyData]);
  const leaderReviewPolicyListCount = leaderReviewPolicyList?.length ?? 0;

  // * mapping fields for accessing review application item
  const mappingLeaderReviewPolicyList = useMemo<
    PoliciesToReviewApplicationPolicyList[]
  >(() => {
    if (!leaderReviewPolicyList) return [];
    return leaderReviewPolicyList.map(item => {
      const policyNo = item?.application?.policyNum ?? '--';
      const policyOwnerNameObj = item?.parties?.find(party =>
        party?.roles?.includes(PartyRole.PROPOSER),
      )?.person?.name;
      const policyPayment =
        item.quotation?.basicInfo?.paymentMode ??
        item.quotation?.plans?.find(item => item?.paymentMode)?.paymentMode;
      const policyPremium =
        policyPayment === PaymentMode.ANNUAL
          ? item.quotation?.summary?.annualPrem
          : policyPayment === PaymentMode.SEMI_ANNUAL
          ? item.quotation?.summary?.semiAnnualPrem
          : policyPayment === PaymentMode.QUARTERLY
          ? item.quotation?.summary?.quarterlyPrem
          : policyPayment === PaymentMode.MONTHLY
          ? item.quotation?.summary?.monthlyPrem
          : policyPayment === PaymentMode.SINGLE ||
            policyPayment === PaymentMode.ONE_TIME
          ? item.quotation?.summary?.totalPrem
          : undefined;
      const latestStatus = item?.latestStatus;

      const frontendKey =
        latestStatus in CaseStatusToNBSubmissionForReviewStatusMap
          ? CaseStatusToNBSubmissionForReviewStatusMap[latestStatus] ??
            'leaderNotResponded'
          : 'leaderNotResponded';
      const pendingReasons = item?.pendingReasons || [];

      return {
        type: 'reviewApplication',
        caseId: item?.caseId ?? '--',
        certNo: policyNo ?? '--',
        certOwner: `${
          policyOwnerNameObj?.firstName ??
          '' + policyOwnerNameObj?.lastName ??
          '--'
        }`,
        contribution: policyPremium,
        paymentMode: policyPayment,
        agentName: item.agent?.fullName ?? '--',
        uwDecision: '',
        status: frontendKey ?? '',
        submissionStatus: item?.submissionResult?.status ?? '',
        submissionDate: item?.submissionResult?.submittedAt
          ? item?.submissionResult?.submittedAt
          : item?.updatedAt ?? '--',
        pendingReasons,
      };
    });
  }, [leaderReviewPolicyList]);

  /**
   * Submission policy
   */
  const { data: submittedPolicyData, isLoading: isSubmittedPolicyLoading } =
    useGetSavedProposals({
      status: statusFilterParams,
      sort_by: '+updatedAt',
      q: undefined,
    });
  const submittedPolicyList: SavedProposal[] = useMemo(() => {
    if (!submittedPolicyData) return [];
    return submittedPolicyData?.pages?.map(page => page?.data)?.flat();
  }, [submittedPolicyData]);
  const submittedPolicyListCount = submittedPolicyList?.length ?? 0;

  // * mapping fields for accessing review application item
  const mappingSubmissionPolicyData = useMemo<
    PoliciesToReviewApplicationPolicyList[]
  >(() => {
    if (!submittedPolicyList) return [];
    return submittedPolicyList.map(item => {
      const policyOwner = item.parties.find(party =>
        party.roles.includes(PartyRole.PROPOSER),
      );
      const policyOwnerNameObj = policyOwner?.person?.name;

      const isPolicyOwnerEntity = policyOwner?.clientType === 'ENTITY';
      const individualPolicyOwner = policyOwnerNameObj
        ? policyOwnerNameObj?.firstName ??
          '' + policyOwnerNameObj?.lastName ??
          ''
        : '--';
      const entityPolicyOwner = policyOwner?.entity?.name ?? '';
      const policyOwnerName = isPolicyOwnerEntity
        ? entityPolicyOwner
        : individualPolicyOwner;

      const policyPremium =
        casePaymentModeToPremiumMatcher({
          paymentMode: item.quotation?.basicInfo?.paymentMode,
          premiumSet: item.quotation?.summary,
        }) ?? 0;

      const policyPaymentMode =
        item.quotation?.basicInfo?.paymentMode ??
        item.quotation?.plans?.find(item => item?.paymentMode)?.paymentMode;

      const latestStatus = item.latestStatus;

      const frontendKey =
        latestStatus in CaseStatusToNBSubmissionForReviewStatusMap
          ? CaseStatusToNBSubmissionForReviewStatusMap[latestStatus] ??
            'leaderNotResponded'
          : 'leaderNotResponded';

      return {
        type: 'submission',
        caseId: item.caseId ?? '--',
        certNo: item.application?.policyNum ?? '--',
        certOwner: policyOwnerName ?? '--',
        contribution: policyPremium,
        paymentMode: policyPaymentMode,
        agentName:
          item.agent?.fullName ?? policyOwner?.person?.name?.fullName ?? '--',
        uwDecision: policyOwner?.uw?.result?.scenario ?? '--',
        status: frontendKey ?? 'leaderNotResponded',
        submissionStatus: item?.submissionResult?.status ?? '',
        submissionDate: item?.submissionResult?.submittedAt
          ? item?.submissionResult?.submittedAt
          : item?.updatedAt ?? '--',
        pendingReasons: item.pendingReasons ?? [],
      };
    });
  }, [submittedPolicyList]);

  const { data: newBusinessPolicyData, isLoading: isNbPolicyLoading } =
    useGetNewBusinessPolicyByAgentId(loginAgentCode || '');

  /**
   * New business policy - Pending / In Progress
   */
  const pendingPolicyList = useMemo<NBPending[]>(() => {
    if (!newBusinessPolicyData) return [];
    return (
      newBusinessPolicyData?.pendings?.map(item => {
        return {
          ...item,
          type: 'pending',
        };
      }) ?? []
    );
  }, [newBusinessPolicyData]);

  const pendingCaseCount = pendingPolicyList?.length ?? 0;

  /**
   * New business policy - Issued
   */
  const issuedPolicyList = useMemo(() => {
    if (!newBusinessPolicyData) return [];
    return newBusinessPolicyData.issueds?.map(data => {
      const item = data as NBIssued;
      return {
        type: 'issued',
        submitDate:
          'submissionDate' in item
            ? item?.submissionDate?.toString() ?? '--'
            : '--',
        status: '',
        issueDate:
          'policyIssuedDate' in item
            ? item?.policyIssuedDate?.toString() ?? '--'
            : null,
        premium: item?.modalPremium,
        paymentMode: item?.paymentMode ?? '',
        policyNumber: item.policyNo,
        policyOwner: item?.displayName?.en ?? '--',
        // the following are not used and just to satisfy the type
        statusCode: null,
        planName: '',
        statusChangeDate: null,
        policyDate: null,
        agentId: '',
        planCategory: '',
        planCode: '',
        firstIssueDate: null,
        clientCode: '',
        dueInDays: null,
        overdueInDays: null,
        paidToDate: null,
        premiumStatusCode: '',
      } satisfies AgentNewBusinessPolicyPHWithFieldsFromOWB;
    });
  }, [newBusinessPolicyData]);

  const issuedCount = issuedPolicyList?.length ?? 0;

  /**
   * All business policy data
   */
  const allNewBusinessPolicyData = useMemo<AllNewBusinessPolicyData[]>(() => {
    return [
      ...(mappingLeaderReviewPolicyList || []),
      ...(mappingSubmissionPolicyData || []),
      ...(pendingPolicyList || []),
      ...(issuedPolicyList || []),
    ];
  }, [
    issuedPolicyList,
    mappingLeaderReviewPolicyList,
    mappingSubmissionPolicyData,
    pendingPolicyList,
  ]);

  /**
   * Search result
   */
  const { searched, setSearched, query, setQuery, result, handleSearch } =
    useSearchPolicies(allNewBusinessPolicyData ?? []);

  const reviewAppResults = result?.filter(
    policy => policy.type === 'reviewApplication',
  );
  const submissionResults = result?.filter(
    policy => policy.type === 'submission',
  );
  const pendingResults = result?.filter(policy => policy.type === 'pending');
  const issuedResults = result?.filter(policy => policy.type === 'issued');

  // CUBEFIB-3170: keith - always pick the chip with data != null, and if all chips have data, then start with the first chip from left to right [Review Application, Submission, Pending, Issued]. Otherwise, the first chip will be selected by default
  useEffect(() => {
    const resultsMap: {
      [key in StatusType['type']]: AllNewBusinessPolicyData[] | undefined;
    } = {
      reviewApplication: reviewAppResults,
      submission: submissionResults,
      pending: pendingResults,
      issued: issuedResults,
    };

    const defaultActiveChip =
      BIZ_STATUS_CHIP_FILTER_CONFIG.find(
        policy => resultsMap[policy.type]?.length ?? 0 > 0,
      ) ?? BIZ_STATUS_CHIP_FILTER_CONFIG[0];

    setActiveChip(defaultActiveChip.type);
    setOrder('newest');
  }, [result]);

  const handleShowSearch = () => {
    setShowSearch(prep => !prep);
    setQuery('');
    setSearched(false);
    showBottomBar();
    setFilteredByReviewStatus([]);
  };

  const handleSetQuery = (q: string) => setQuery(q);
  const handleResetSearch = () => setSearched(false);

  const animatedSearchStyle = useAnimatedStyle(() => ({
    width: withTiming(showSearch ? '85%' : 0, {
      duration: theme.animation.duration,
    }),
  }));

  /**
   *  Filtered search results by business status
   */

  const isFilterButtonShown =
    activeChip === 'reviewApplication' || activeChip === 'submission';

  const isFiltered = isFilterButtonShown && isFilteredByReviewStatus;

  const filteredLeaderReviewPolicyList = useMemo(() => {
    if (!result) return [];

    const sortedList: AllNewBusinessPolicyData[] = [...result].sort((a, b) => {
      const dateField: keyof AllNewBusinessPolicyData =
        activeChip === 'reviewApplication' || activeChip === 'submission'
          ? 'submissionDate'
          : activeChip === 'pending'
          ? 'policyIssuedDate'
          : activeChip === 'issued'
          ? 'issueDate'
          : 'submissionDate';

      const dateA = new Date(a[dateField] ?? '');
      const dateB = new Date(b[dateField] ?? '');
      a;
      return sortDateHandler({
        aDate: dateA,
        bDate: dateB,
        sortOrder: order,
      });
    });

    const filteredListByBusinessStatus = activeChip
      ? sortedList.filter(item =>
          item.type ? activeChip?.includes(item.type) : false,
        )
      : sortedList;

    return filterByReviewStatus?.length
      ? filteredListByBusinessStatus.filter(policy =>
          policy?.submissionStatus
            ? filterByReviewStatus.includes(policy?.status)
            : false,
        )
      : filteredListByBusinessStatus;
  }, [
    result,
    isFiltered,
    isFilteredByReviewStatus,
    isReviewApplicationTabVisible,
    activeChip,
    order,
    filterByReviewStatus,
  ]);

  const resultCount = filteredLeaderReviewPolicyList?.length ?? 0;

  /**
   * Tabs config
   */
  const REVIEW_APPLICATION_TAB_CONFIG = [
    {
      status: 'reviewApplication',
      label: 'newBusiness.leaderReviewApplication',
      icon: <Icon.Document size={ICON_SIZE} fill={colors.background} />,
      count: leaderReviewPolicyListCount,
      countIcon: (
        <Icon.WarningFill
          size={SMALL_ICON_SIZE}
          fill={colors.palette.alertRed}
        />
      ),
      countColor: colors.palette.fwdDarkGreen[100],
      onPress: () => navigation.navigate('AgentPoliciesReview'),
      isLoading: isLeaderReviewPolicyLoading,
    },
  ] satisfies TabConfig[];

  const STATUS_TAB_CONFIG = [
    {
      status: 'submission',
      label: 'newBusiness.reviewSubmission',
      icon: <Icon.DocumentCopy size={ICON_SIZE} fill={colors.background} />,
      count: submittedPolicyListCount,
      countIcon: undefined,
      countColor: undefined,
      isLoading: isSubmittedPolicyLoading,
      onPress: () => {
        navigation.push('PoliciesNewBusiness', {
          screen: 'NBList',
          params: {
            status: 'submission',
            viewingAgentCode: undefined, // * Update if necessary
          },
        });
      },
    },
    {
      status: 'pending',
      label: 'newBusiness.pending',
      icon: <Icon.Warning size={ICON_SIZE} fill={colors.background} />,
      count: pendingCaseCount,
      countIcon: (
        <Icon.WarningFill
          size={SMALL_ICON_SIZE}
          fill={colors.palette.alertRed}
        />
      ),
      countColor: undefined,
      isLoading: isNbPolicyLoading,
      onPress: () => {
        navigation.push('PoliciesNewBusiness', {
          screen: 'NBList',
          params: {
            status: 'pending',
            viewingAgentCode: undefined, // * Update if necessary
          },
        });
      },
    },
    {
      status: 'issued',
      label: 'newBusiness.issued',
      icon: <Icon.TickCircle size={ICON_SIZE} fill={colors.background} />,
      count: issuedCount,
      countIcon: undefined,
      countColor: undefined,
      isLoading: isNbPolicyLoading,
      onPress: () => {
        navigation.push('PoliciesNewBusiness', {
          screen: 'NBList',
          params: {
            status: 'issued',
            viewingAgentCode: undefined, // * Update if necessary
          },
        });
      },
    },
  ] satisfies TabConfig[];

  /**
   * Reset search and filter when screen is unfocused
   */
  useFocusEffect(
    useCallback(() => {
      return () => {
        setActiveChip(BIZ_STATUS_CHIP_FILTER_CONFIG[0].type);
        setQuery('');
        setSearched(false);
        setShowSearch(false);
        setOrder('newest');
      };
    }, []),
  );
  const commonBgColor = colors.palette.fwdGrey[50];

  if (showSearch) {
    return (
      <Column flex={1} bgColor={commonBgColor}>
        <Column px={PADDING_X}>
          <PolicySearch
            show={showSearch}
            handleShowSearch={handleShowSearch}
            animated={animatedSearchStyle}
            query={query}
            searched={searched}
            handleSetQuery={handleSetQuery}
            handleSetSearch={handleSearch}
            handleResetSearch={handleResetSearch}
          />
        </Column>

        {searched && (
          <Box flex={1}>
            {/* ----- Chips */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{
                marginHorizontal: PADDING_X,
                marginTop: space[2],
                maxHeight: sizes[10],
              }}
              contentContainerStyle={{
                gap: space[1],
                alignItems: 'center',
              }}>
              {BIZ_STATUS_CHIP_FILTER_CONFIG.map(({ type, label }) => (
                <Chip
                  key={type}
                  label={label}
                  focus={activeChip === type}
                  onPress={() => {
                    setActiveChip(prevValue =>
                      prevValue === type ? undefined : type,
                    );
                    setOrder('newest');
                  }}
                />
              ))}
            </ScrollView>

            {/* ----- Sort by date and filter modal button */}
            <Row
              px={PADDING_X}
              pt={space[1]}
              pb={space[2]}
              justifyContent="space-between">
              <CountAndSortHeader
                title="Search result"
                listDataCount={resultCount}
                order={order}
                onPress={() =>
                  setOrder(order === 'newest' ? 'oldest' : 'newest')
                }
                isFiltered={isFiltered}
              />
              {/* Filter feature appears only in reviewApplication and submission */}
              {isFilterButtonShown && (
                <TouchableOpacity
                  onPress={() => setOpenFilterPanel(true)}
                  style={{ marginLeft: space[4] }}>
                  {isFilteredByReviewStatus && <FilterDot />}
                  <Icon.Filter fill={colors.secondary} />
                </TouchableOpacity>
              )}
            </Row>

            <View style={{ flex: 1 }}>
              <FlashList
                keyExtractor={(item, index) => `${index}-${item?.type}`}
                bounces={false}
                // extraData={filteredLeaderReviewPolicyList}
                data={filteredLeaderReviewPolicyList}
                renderItem={({ item }) => {
                  switch (item.type) {
                    case 'reviewApplication':
                    case 'submission':
                      return (
                        <ReviewApplicationListItem
                          {...item}
                          onPress={debounce(() => {
                            navigation.push('PoliciesNewBusiness', {
                              screen: 'NBDetail',
                              params: {
                                type: 'case',
                                caseId: item.caseId ?? '',
                                reviewStatus:
                                  item?.status as NBPolicyLeaderReviewStatus,
                                submissionStatus:
                                  (item?.submissionStatus as NBPolicyLeaderReviewSubmissionStatus) ??
                                  '',
                              },
                            });
                          }, 300)}
                          contribution={
                            item?.contribution !== null
                              ? Number(item?.contribution)
                              : undefined
                          }
                          isSelectAll={false}
                          selectMode={false}
                        />
                      );
                    case 'pending':
                      return (
                        <NBListCardSet.pendingV2
                          item={item as NBPending}
                          onPress={debounce(() => {
                            return navigation.push('PoliciesNewBusiness', {
                              screen: 'NBDetail',
                              params: {
                                type: 'policy',
                                policyId: item?.policyNo ?? '',
                                status: item?.policyStatus as PolicyStatus,
                              },
                            });
                          }, 300)}
                          disabled={false}
                        />
                      );
                    case 'issued':
                      return (
                        <NBListCardSet.issued
                          item={
                            item as AgentNewBusinessPolicyPHWithFieldsFromOWB
                          }
                          onPress={debounce(() => {
                            return navigation.push('PoliciesNewBusiness', {
                              screen: 'NBDetail',
                              params: {
                                type: 'policy',
                                policyId: item?.policyNumber ?? '',
                                status: 'issued',
                              },
                            });
                          }, 300)}
                          disabled={false}
                        />
                      );
                    default:
                      return null;
                  }
                }}
                contentContainerStyle={{
                  paddingBottom: space[13],
                  paddingHorizontal: PADDING_X,
                }}
                estimatedItemSize={149}
                ListEmptyComponent={
                  <>
                    {isFiltered ? (
                      <Placeholder.FilteredEmptyRecord />
                    ) : (
                      <Placeholder.SearchEmptyRecord />
                    )}
                  </>
                }
                ItemSeparatorComponent={() => <Row paddingBottom={space[2]} />}
                onScrollBeginDrag={e => {
                  const startOffset = e.nativeEvent.contentOffset.y;
                  lastScrollY.value = startOffset;
                }}
                onScrollEndDrag={e => {
                  const endOffset = e.nativeEvent.contentOffset.y;
                  if (endOffset === 0) {
                    showBottomBar();
                  } else {
                    if (endOffset > lastScrollY.value) {
                      hideBottomBar();
                    } else {
                      showBottomBar();
                    }
                  }
                  lastScrollY.value = endOffset;
                }}
              />
            </View>

            <SubmissionStatusActionPanel
              visible={openFilterPanel}
              handleClose={() => setOpenFilterPanel(false)}
              stateValue={filterByReviewStatus}
              setStateValue={setFilteredByReviewStatus}
            />
          </Box>
        )}
      </Column>
    );
  }

  return (
    <Column px={PADDING_X} bgColor={commonBgColor} flex={1}>
      <Row py={space[5]} alignItems="center" justifyContent="space-between">
        <H7 fontWeight="bold" children={t('policy:newBusiness.status')} />
        <Row alignItems="center" gap={space[2]}>
          <H7 fontWeight="bold" children={t('policy:case')} />
          <TouchableOpacity onPress={handleShowSearch}>
            <Icon.Search size={ICON_SIZE} fill={colors.onBackground} />
          </TouchableOpacity>
        </Row>
      </Row>

      <Column gap={space[4]}>
        {isReviewApplicationTabVisible && (
          <Column
            p={space[4]}
            bgColor={colors.background}
            borderRadius={borderRadius.large}>
            {REVIEW_APPLICATION_TAB_CONFIG?.map(item => (
              <TabButtonV2
                key={item?.status}
                {...item}
                isLoading={item?.isLoading}
              />
            ))}
          </Column>
        )}

        <TabContainer>
          {STATUS_TAB_CONFIG?.map((item, idx) => (
            <Fragment key={item?.status}>
              <TabButtonV2
                {...item}
                onPress={item?.onPress}
                isLoading={item?.isLoading}
              />
              {idx == STATUS_TAB_CONFIG.length - 1 ? null : <Line />}
            </Fragment>
          ))}
        </TabContainer>
      </Column>
    </Column>
  );
}

const FilterDot = styled.View(({ theme }) => ({
  position: 'absolute',
  height: theme.sizes[2],
  width: theme.sizes[2],
  backgroundColor: theme.colors.primary,
  borderRadius: theme.borderRadius.full,
  borderColor: theme.colors.primary,
  borderWidth: 1,
  right: 0,
  top: 0,
  zIndex: 99,
}));
