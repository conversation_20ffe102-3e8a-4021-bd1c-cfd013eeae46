import React, { Fragment, useContext, useMemo } from 'react';
import { useGetPolicyDetail } from 'hooks/useGetPolicyDetail';
import { useTranslation } from 'react-i18next';
import { PoliciesDetailTabParamList } from 'types';
import { MaterialTopTabScreenProps } from '@react-navigation/material-top-tabs';
import {
  capitalFirst,
  formatSales,
  dateFormatUtil,
} from 'utils/helper/formatUtil';
import useBoundStore from 'hooks/useBoundStore';
import { Coverages, STATUS_PENDING, posStatusArray } from 'types/policy';
import { useGetNewBusinessPolicyByAgentIdPH } from 'hooks/useGetPolicyList';
import PoliciesScreenContext from 'screens/PoliciesScreen/PoliciesScreenContext';
import { useGetTeamNewBusinessPolicyByAgentId } from 'hooks/useGetTeam';
import ScrollScreenWrapper from 'features/policy/components/PolicyDetails/ScrollScreenWrapper';
import {
  Container,
  Currency,
  InnerContainer,
  Item,
  ItemContainer,
  Label,
  LabelContainer,
  LabelStyle,
  Line,
} from 'features/policy/components/PolicyDetails/StyledComponents';

type DetailTabScreenProps = MaterialTopTabScreenProps<
  PoliciesDetailTabParamList,
  'Coverage'
>;

export default function Coverage({ navigation, route }: DetailTabScreenProps) {
  const { status, policyNo } = route.params;
  const { viewingAgentCode } = useContext(PoliciesScreenContext);

  const { t } = useTranslation(['policy']);

  const agentCode = useBoundStore().auth.agentCode;

  const { data: teamNBPolicies } =
    useGetTeamNewBusinessPolicyByAgentId(viewingAgentCode);

  const { data: agentNBPolicies } = useGetNewBusinessPolicyByAgentIdPH(
    agentCode ?? undefined,
  );

  const policyList = viewingAgentCode ? teamNBPolicies : agentNBPolicies;

  const { data } = useGetPolicyDetail({
    policyNo,
    policyDomain: posStatusArray?.find(ele => ele == status)
      ? 'POS'
      : 'NEW_BUSINESS',
  });

  const policyDetails = useMemo(() => {
    return data?.coverages;
  }, [data]);

  const policyStatus = useMemo(() => {
    if (status !== 'pending')
      return policyDetails ? capitalFirst(policyDetails[0].policyStatus) : '';

    return (
      policyList?.find(item => item.status === STATUS_PENDING) &&
      policyList?.find(item => item.policyNumber === policyNo)?.statusCode
    );
  }, [policyList]);

  const currency: string | undefined = useMemo(() => {
    return data?.currency || 'PHP';
  }, [data]);

  if (!policyDetails || !policyDetails.length) {
    return (
      <ScrollScreenWrapper>
        <Container>
          <InnerContainer>
            <LabelContainer>
              <LabelStyle>
                <Label>{t('policy:basicPlan')}</Label>
                <ItemContainer></ItemContainer>
              </LabelStyle>
              <LabelStyle>
                <Label>{t('policy:sumInsured')}</Label>
              </LabelStyle>
              <LabelStyle>
                <Label>{t('policy:issueDate')}</Label>
                <ItemContainer></ItemContainer>
              </LabelStyle>
              <LabelStyle>
                <Label>{t('policy:policyStatus')}</Label>
                <ItemContainer></ItemContainer>
              </LabelStyle>
              <LabelStyle>
                <Label>{t('policy:modalPremium')}</Label>
                <ItemContainer></ItemContainer>
              </LabelStyle>
            </LabelContainer>
          </InnerContainer>
        </Container>
      </ScrollScreenWrapper>
    );
  }

  return (
    <ScrollScreenWrapper>
      <Container>
        <InnerContainer>
          {status === 'pending' ? (
            <LabelContainer>
              <LabelStyle>
                <Label>{t('policy:basicPlan')}</Label>
                <ItemContainer>
                  <Item>{policyDetails && policyDetails[0].basicPlan}</Item>
                </ItemContainer>
              </LabelStyle>
              <LabelStyle>
                <Label>{t('policy:sumInsured')}</Label>
                <ItemContainer>
                  {policyDetails[0]?.sumAssured !== null && (
                    <Currency>{currency}</Currency>
                  )}
                  <Item>{formatSales(policyDetails[0]?.sumAssured)}</Item>
                </ItemContainer>
              </LabelStyle>
              <LabelStyle>
                <Label>{t('policy:issueDate')}</Label>
                <ItemContainer>
                  <Item>
                    {policyDetails[0]?.issueDate &&
                      dateFormatUtil(policyDetails[0]?.issueDate)}
                  </Item>
                </ItemContainer>
              </LabelStyle>
              <LabelStyle>
                <Label>{t('policy:policyStatus')}</Label>
                <ItemContainer>
                  <Item>{policyStatus}</Item>
                </ItemContainer>
              </LabelStyle>
              <LabelStyle>
                <Label>{t('policy:modalPremium')}</Label>
                <ItemContainer>
                  {policyDetails[0]?.modalPremium === 0 ? null : (
                    <Currency>{currency}</Currency>
                  )}

                  <Item>
                    {policyDetails[0]?.modalPremium &&
                      formatSales(policyDetails[0].modalPremium)}
                  </Item>
                </ItemContainer>
              </LabelStyle>
            </LabelContainer>
          ) : (
            <>
              {policyDetails &&
                policyDetails.length > 0 &&
                policyDetails.map((coverage: Coverages, index: number) => {
                  return (
                    <Fragment key={index}>
                      <LabelContainer>
                        <LabelStyle key={index + 'coverage'}>
                          <Label>
                            {index > 0
                              ? t('policy:riderName')
                              : t('policy:basicPlan')}
                          </Label>
                          <ItemContainer>
                            <Item>{coverage?.basicPlan}</Item>
                          </ItemContainer>
                        </LabelStyle>
                        <LabelStyle>
                          <Label>{t('policy:sumInsured')}</Label>
                          <ItemContainer>
                            {coverage?.sumAssured !== null && (
                              <Currency>{currency}</Currency>
                            )}
                            <Item>{formatSales(coverage?.sumAssured)}</Item>
                          </ItemContainer>
                        </LabelStyle>
                        <LabelStyle>
                          <Label>{t('policy:issueDate')}</Label>
                          <ItemContainer>
                            <Item>
                              {coverage?.issueDate &&
                                dateFormatUtil(coverage?.issueDate)}
                            </Item>
                          </ItemContainer>
                        </LabelStyle>
                        <LabelStyle>
                          <Label>{t('policy:policyStatus')}</Label>
                          <ItemContainer>
                            <Item>
                              {coverage?.policyStatus &&
                                capitalFirst(coverage.policyStatus)}
                            </Item>
                          </ItemContainer>
                        </LabelStyle>
                        <LabelStyle>
                          <Label>{t('policy:modalPremium')}</Label>
                          <ItemContainer>
                            {coverage?.modalPremium !== null && (
                              <Currency>{currency}</Currency>
                            )}
                            <Item>
                              {coverage?.modalPremium &&
                                formatSales(coverage.modalPremium)}
                            </Item>
                          </ItemContainer>
                        </LabelStyle>
                      </LabelContainer>
                      {index === policyDetails.length - 1 ? null : <Line />}
                    </Fragment>
                  );
                })}
            </>
          )}
        </InnerContainer>
      </Container>
    </ScrollScreenWrapper>
  );
}
