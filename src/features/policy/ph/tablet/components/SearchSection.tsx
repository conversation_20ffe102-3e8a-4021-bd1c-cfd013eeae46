import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import CustomSearchingBar from 'components/CustomSearchingBar';
import { Box, Typography } from 'cube-ui-components';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import { useGetPosPolicyByAgentIdPH } from 'hooks/useGetPolicyList';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { SortDirectionKeys } from 'types/eRecruit';
import {
  AgentNewBusinessPolicyPH,
  AgentPosPolicyPH,
  POSStatus,
  STATUS_DUE,
  STATUS_FREELOOK,
  STATUS_ISSUED,
  STATUS_OVERDUE,
  STATUS_PENDING,
  STATUS_SUBMISSION,
} from 'types/policy';
import IssueTableSet from '../NewBusiness/Issue/IssueTable';
import PendingTableSet from '../NewBusiness/Pending/PendingTable';
import SubmissionTableSet from '../NewBusiness/Submission/SubmissionTable';
import DueTableSet from '../POS/Due/DueTable';
import FreelookTableSet from '../POS/Freelook/FreelookTable';
import OverdueTableSet from '../POS/Overdue/OverdueTable';

export default function SearchSection({
  data,
  setIsSearching,
  isDefaultConfig,
}: {
  data: AgentNewBusinessPolicyPH[] | AgentPosPolicyPH[];
  setIsSearching: React.Dispatch<React.SetStateAction<boolean>>;
  isDefaultConfig: boolean;
}) {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { data: posData } = useGetPosPolicyByAgentIdPH('');
  const { space, colors } = useTheme();
  const { t } = useTranslation('policy');

  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  const [searchKeyWords, setSearchKeyWords] = useState<string>('');
  type PosStatus =
    | typeof STATUS_DUE
    | typeof STATUS_OVERDUE
    | typeof STATUS_FREELOOK;

  const [filterValue, setFilterValue] = useState<Record<PosStatus, boolean>>({
    [STATUS_DUE]: false,
    [STATUS_OVERDUE]: false,
    [STATUS_FREELOOK]: false,
  });

  const handleSetFilter = (isActive: boolean, filter: PosStatus) => {
    setFilterValue({
      ...filterValue,
      [filter]: isActive,
    });
  };

  const isSubmission = useMemo(
    () => data.some(item => item.status === STATUS_SUBMISSION),
    [data],
  );
  const isPending = useMemo(
    () => data.some(item => item.status === STATUS_PENDING),
    [data],
  );
  const isIssued = useMemo(
    () => data.some(item => item.status === STATUS_ISSUED),
    [data],
  );
  const isNB = isSubmission || isPending || isIssued;

  const isDue = useMemo(
    () => data.some(item => item.status === STATUS_DUE),
    [data],
  );
  const isOverdue = useMemo(
    () => data.some(item => item.status === STATUS_OVERDUE),
    [data],
  );
  const isFreelook = useMemo(
    () => data.some(item => item.status === STATUS_FREELOOK),
    [data],
  );

  const searchResultList = useMemo(() => {
    // * backend hook allows q parameter to search, but not search the target fields, ie policyOwner, policyNo, fromAgent
    // * so the search is done on the frontend for now
    // if (!isNB) {
    //   if (!posData) return [];
    //   const searchResultData = posData
    //     .filter(item => {
    //       const { status, policyOwner, policyNumber } = item;
    //       const lowerSearchKeyWords = searchKeyWords.toLowerCase();
    //       const isMatchedStatus =
    //         (!filterValue.DUE &&
    //           !filterValue.OVERDUE &&
    //           !filterValue.FREELOOK) ||
    //         (filterValue.DUE && status === STATUS_DUE) ||
    //         (filterValue.OVERDUE && status === STATUS_OVERDUE) ||
    //         (filterValue.FREELOOK && status === STATUS_FREELOOK);
    //       const isMatchedPolicyInfo =
    //         policyOwner?.toLowerCase().includes(lowerSearchKeyWords) ||
    //         policyNumber?.toLowerCase().includes(lowerSearchKeyWords);
    //       return isMatchedStatus && isMatchedPolicyInfo;
    //     })
    //     .sort((a, b) => {
    //       const dateA = a.submitDate ? new Date(a.submitDate).getTime() : 0;
    //       const dateB = b.submitDate ? new Date(b.submitDate).getTime() : 0;
    //       return order === 'newest' ? dateB - dateA : dateA - dateB;
    //     });
    //   return searchResultData;
    // }

    const searchResultData = data.filter(
      item =>
        item.policyOwner
          ?.toLowerCase()
          .includes(searchKeyWords.toLowerCase()) ||
        item.policyNumber?.toLowerCase().includes(searchKeyWords.toLowerCase()),
    ) as AgentNewBusinessPolicyPH[] | AgentPosPolicyPH[];

    return order === 'newest' ? searchResultData : searchResultData.reverse();
  }, [data, order, searchKeyWords]);

  return (
    <AnimatedViewWrapper
      style={{
        flex: 1,
        paddingTop: space[5],
        paddingHorizontal: space[6],
        backgroundColor: colors.palette.fwdGrey[50],
      }}>
      <CustomSearchingBar
        onExitSearch={() => setIsSearching(false)}
        setSearchKeyWords={setSearchKeyWords}
        placeholderText={t('search.nbSubmission.placeholder')}
      />

      {searchKeyWords.trim().length > 0 && (
        <Box flex={1}>
          <Box pt={space[6]} pb={space[3]}>
            <Typography.H7
              fontWeight="bold"
              style={{ paddingBottom: space[3] }}>
              {t('search.searchResult') + ` (${searchResultList.length})`}
            </Typography.H7>
            {isNB ? (
              <></>
            ) : (
              // <Row pb={space[3]} alignItems="center" gap={space[2]}>
              //   <TagButton
              //     isActive={filterValue.DUE}
              //     onChange={isActive => handleSetFilter(isActive, STATUS_DUE)}
              //     label={t('pos.due')}
              //   />
              //   <TagButton
              //     isActive={filterValue.OVERDUE}
              //     onChange={isActive =>
              //       handleSetFilter(isActive, STATUS_OVERDUE)
              //     }
              //     label={t('pos.overdue')}
              //   />
              //   <TagButton
              //     isActive={filterValue.FREELOOK}
              //     onChange={isActive =>
              //       handleSetFilter(isActive, STATUS_FREELOOK)
              //     }
              //     label={t('pos.freelook')}
              //   />
              // </Row>
              <></>
            )}
            <Typography.H8 color={colors.palette.fwdGreyDarkest}>
              {/* {t('newBusiness.totalCounts', {
                count: searchResultList.length ?? 0,
              })}
              {'  '}|{'  '} */}
              {t('pendingStaticText')}
            </Typography.H8>
          </Box>
          {isNB ? (
            isSubmission ? (
              <SubmissionTableSet.Table
                HeaderComponent={() => (
                  <SubmissionTableSet.Header
                    sortOrder={order}
                    setSortOrder={setOrder}
                    isDefaultConfig={isDefaultConfig}
                  />
                )}
                data={searchResultList as AgentNewBusinessPolicyPH[]}
                isDefaultConfig={isDefaultConfig}
              />
            ) : isPending ? (
              <PendingTableSet.Table
                HeaderComponent={() => (
                  <PendingTableSet.Header
                    sortOrder={order}
                    setSortOrder={setOrder}
                    isDefaultConfig={isDefaultConfig}
                  />
                )}
                data={searchResultList as AgentNewBusinessPolicyPH[]}
                isDefaultConfig={isDefaultConfig}
              />
            ) : (
              isIssued && (
                <IssueTableSet.Table
                  HeaderComponent={() => (
                    <IssueTableSet.Header
                      sortOrder={order}
                      setSortOrder={setOrder}
                      isDefaultConfig={isDefaultConfig}
                    />
                  )}
                  data={searchResultList as AgentNewBusinessPolicyPH[]}
                  isDefaultConfig={isDefaultConfig}
                />
              )
            )
          ) : isFreelook ? (
            <FreelookTableSet.Table
              HeaderComponent={() => (
                <FreelookTableSet.Header
                  sortOrder={order}
                  setSortOrder={setOrder}
                  isDefaultConfig={isDefaultConfig}
                />
              )}
              onPressItem={item => {
                return navigation.navigate('ExistingPolicyDetail', {
                  type: 'policy',
                  policyId: item.policyNumber,
                  status: item.status.toLowerCase() as POSStatus,
                });
              }}
              data={searchResultList as AgentPosPolicyPH[]}
              isDefaultConfig={isDefaultConfig}
            />
          ) : isDue ? (
            <DueTableSet.Table
              HeaderComponent={() => (
                <DueTableSet.Header
                  sortOrder={order}
                  setSortOrder={setOrder}
                  isDefaultConfig={isDefaultConfig}
                />
              )}
              data={searchResultList as AgentPosPolicyPH[]}
              isDefaultConfig={isDefaultConfig}
            />
          ) : isOverdue ? (
            <OverdueTableSet.Table
              HeaderComponent={() => (
                <OverdueTableSet.Header
                  sortOrder={order}
                  setSortOrder={setOrder}
                  isDefaultConfig={isDefaultConfig}
                />
              )}
              data={searchResultList as AgentPosPolicyPH[]}
              isDefaultConfig={isDefaultConfig}
            />
          ) : (
            <></>
          )}
        </Box>
      )}
    </AnimatedViewWrapper>
  );
}
