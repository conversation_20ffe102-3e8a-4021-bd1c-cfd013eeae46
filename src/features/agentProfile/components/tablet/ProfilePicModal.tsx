import React, { useEffect, useRef, useState } from 'react';
import { Modal } from 'react-native';
import { useTheme } from '@emotion/react';
import { Box, Button } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';

export default function ProfilePicModal({
  dismiss,
  takePhoto,
  selectFromAlbum,
  isVisible,
}: {
  dismiss: () => void;
  takePhoto: () => void;
  selectFromAlbum: () => void;
  isVisible: boolean;
}) {
  const { t } = useTranslation('agentProfile');
  const { space, colors, borderRadius } = useTheme();

  return (
    <Modal
      visible={isVisible}
      onDismiss={dismiss}
      transparent
      statusBarTranslucent
      animationType="fade">
      <Box
        backgroundColor={'rgba(0, 0, 0, 0.5)'}
        flex={1}
        alignItems="center"
        justifyContent="center">
        <Box
          p={space[6]}
          borderRadius={borderRadius.large}
          backgroundColor={colors.background}>
          <Button
            style={{
              width: 250,
              height: 52,
              marginVertical: 8,
            }}
            text={t('takePhoto')}
            onPress={takePhoto}
          />
          <Button
            style={{
              width: 250,
              height: 52,
              marginVertical: 8,
            }}
            text={t('selectFromAlbum')}
            onPress={selectFromAlbum}
          />
          <Button
            style={{
              width: 250,
              height: 52,
              marginVertical: 8,
            }}
            variant="secondary"
            text={t('cancel')}
            onPress={dismiss}
          />
        </Box>
      </Box>
    </Modal>
  );
}
