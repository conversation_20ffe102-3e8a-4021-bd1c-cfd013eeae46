import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { DependentConfiguration } from 'features/fna/types/lifeJourney';
import { Dependent, DependentRelationship } from 'types/case';
import { memo, useCallback } from 'react';
import DependentsTablet from './Dependents.tablet';
import { shallow } from 'zustand/shallow';
import {
  initialEducationKidGoal,
  initialFnaState,
  useFnaStore,
} from 'features/fna/utils/store/fnaStore';
import { Icon } from 'cube-ui-components';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import DependentsPhone from './Dependents.phone';
import { cloneDeep } from 'utils/helper/objectUtil';

export type DependentProps = {
  config: DependentConfiguration;
  data?: Dependent[];
  onChange?: (dependents: Dependent[]) => void;
  onBack?: () => void;
  orderNumber?: number;
} & ReadonlyProps;

export const DEPENDENT_ICONS = {
  KID: Icon.Kid,
  SPOUSE: Icon.Ring,
  PARENT: Icon.Retirement,
  SIBLING: Icon.Team,
};

const Dependents = memo((props: DependentProps) => {
  const {
    dependents,
    updateDependents,
    updatePreSyncDependents,
    updateEducationGoal,
    updateLifeStage,
  } = useFnaStore(
    state => ({
      dependents: state.lifeJourney.dependents,
      updateDependents: state.updateDependents,
      updatePreSyncDependents: state.updatePreSyncDependents,
      updateEducationGoal: state.updateEducationGoal,
      updateLifeStage: state.updateLifeStage,
    }),
    shallow,
  );

  const onChange = (dependents: Dependent[]) => {
    updateDependents(dependents);
    updatePreSyncDependents(dependents);
    const kidDependent = dependents.filter(
      dependent => dependent.relationship === DependentRelationship.kid,
    );
    updateEducationGoal({
      ...initialFnaState.educationGoal,
      goals:
        kidDependent.length > 0
          ? kidDependent.map(kid => ({
              ...initialEducationKidGoal,
              dependentId: kid.id,
              gender: kid.gender || null,
              childAge: kid.age,
            }))
          : [cloneDeep(initialEducationKidGoal)],
      numberOfKids: kidDependent.length || 1,
    });
  };

  const unsetLifeStage = useCallback(() => {
    if (dependents.length === 0) {
      updateLifeStage(null);
    }
  }, [dependents, updateLifeStage]);

  return (
    <DeviceBasedRendering
      phone={
        <DependentsPhone
          {...props}
          data={dependents}
          onChange={onChange}
          onBack={unsetLifeStage}
          readonly={Boolean(props.readonly)}
          orderNumber={props.orderNumber || 0}
        />
      }
      tablet={
        <DependentsTablet
          {...props}
          data={dependents}
          onChange={onChange}
          onBack={unsetLifeStage}
          readonly={Boolean(props.readonly)}
          orderNumber={props.orderNumber || 0}
        />
      }
    />
  );
});

export default Dependents;
