import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { getOptionListValue } from 'constants/optionList';
import {
  Body,
  Box,
  Button,
  Center,
  H6,
  Icon,
  LargeBody,
  RadioButton,
  Row,
} from 'cube-ui-components';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, TextInput, TouchableOpacity } from 'react-native';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { CountryUni } from 'types/optionList';
import { formatCurrency } from 'utils';
import MarkdownTextMore from 'components/MarkdownTextMore';
import { InternalUniversityLocationPickerProps } from './UniversityLocationPicker';
import UniversityEmptyIcon from './UniversityEmptyIcon';
import { COUNTRY_FLAGS } from './countryFlags';

export default function UniversityLocationPickerTablet({
  visible,
  filteredList,
  searchColor,
  searchInputRef,
  query,
  setQuery,
  universityLocation,
  setUniversityLocation,
  isFocusingSearchInput,
  onFocusSearchInput,
  onBlurSearchInput,
  onDone,
  onDismiss,
}: InternalUniversityLocationPickerProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors, borderRadius } = useTheme();

  const { height, width } = useSafeAreaFrame();

  return (
    <CFFModal visible={visible} dismissable onDismiss={onDismiss}>
      <Box
        alignSelf="center"
        w={width - space[40]}
        h={height - 58}
        backgroundColor={colors.background}
        p={space[12]}
        pr={space[8]}
        borderRadius={borderRadius.large}>
        <Box mr={space[4]}>
          <H6 fontWeight="bold">
            {t('fna:savingsGoals.education.universityLocation.title')}
          </H6>
          <MarkdownTextMore
            fullText={t('fna:savingsGoals.education.disclaimer.expanded')}
            shortText={t('fna:savingsGoals.education.disclaimer.collapsed')}
            style={{ marginTop: space[3] }}
          />
          <Row mt={space[6]} mb={space[4]} alignItems="center" gap={space[2]}>
            {Boolean(query) && (
              <TouchableOpacity
                hitSlop={ICON_HIT_SLOP}
                onPress={() => {
                  searchInputRef.current?.blur();
                }}>
                <Icon.ArrowLeft fill={colors.secondary} />
              </TouchableOpacity>
            )}
            <Row
              alignItems="center"
              rounded={borderRadius.full}
              borderColor={
                !isFocusingSearchInput && query
                  ? colors.palette.fwdDarkGreen[20]
                  : colors.primary
              }
              border={1}
              gap={space[2]}
              py={space[3] - 2}
              pl={space[4]}
              pr={space[3]}>
              {!isFocusingSearchInput && !query && (
                <Icon.Search size={24} fill={colors.primary} />
              )}
              <SearchInput
                ref={searchInputRef}
                value={query}
                onChangeText={setQuery}
                autoCorrect={false}
                placeholder={t(
                  'fna:savingsGoals.education.universityLocation.search',
                )}
                placeholderTextColor={searchColor}
                cursorColor={searchColor}
                selectionColor={searchColor}
                style={{
                  color: searchColor,
                }}
                onFocus={onFocusSearchInput}
                onBlur={onBlurSearchInput}
              />
              {Boolean(query) && (
                <TouchableOpacity
                  hitSlop={ICON_HIT_SLOP}
                  onPress={() => {
                    setQuery('');
                  }}>
                  <Icon.CloseCircle size={20} fill={searchColor} />
                </TouchableOpacity>
              )}
            </Row>
          </Row>
        </Box>
        {filteredList && filteredList.length > 0 && (
          <Row>
            <Box width={22} mr={space[5]} />
            <Header color={colors.palette.fwdGreyDarker}>
              {t('fna:savingsGoals.education.countryName')}
            </Header>
            <Header color={colors.palette.fwdGreyDarker}>
              {t('fna:savingsGoals.education.avgTuitionFee')}
            </Header>
            <Header color={colors.palette.fwdGreyDarker}>
              {t('fna:savingsGoals.education.avgLivingExpenses')}
            </Header>
          </Row>
        )}
        <UniversityLocationList
          data={filteredList || []}
          keyExtractor={getOptionListValue}
          ItemSeparatorComponent={Separator}
          renderItem={info => (
            <UniversityLocation
              item={info.item}
              selected={info.item.value === universityLocation}
              onPress={() => setUniversityLocation(info.item.value)}
            />
          )}
          ListEmptyComponent={EmptyResult}
        />
        <Row
          justifyContent="center"
          alignItems="center"
          gap={space[4]}
          mr={space[4]}
          mt={space[6]}>
          <ActionButton
            variant="secondary"
            size="medium"
            text={t('fna:cancel')}
            onPress={onDismiss}
          />
          <ActionButton
            size="medium"
            text={t('fna:confirm')}
            disabled={!universityLocation}
            onPress={onDone}
          />
        </Row>
      </Box>
    </CFFModal>
  );
}

const UniversityLocationList = styled(FlatList)(({ theme }) => ({
  paddingRight: theme.space[4],
  flexShrink: 1,
})) as unknown as typeof FlatList;

const UniversityLocation = ({
  item,
  selected,
  onPress,
}: {
  item: CountryUni;
  selected?: boolean;
  onPress?: () => void;
}) => {
  const { space } = useTheme();

  return (
    <UniversityLocationPressable onPress={onPress}>
      <ItemRadio selected={selected} value={item.value} label="" />
      <Row flexBasis={'33%'} gap={space[1]} alignItems="center">
        {React.createElement(
          COUNTRY_FLAGS[item.value as keyof typeof COUNTRY_FLAGS],
        )}
        <LargeBody style={{ flex: 1 }}>{item.label}</LargeBody>
      </Row>
      <RowText>
        {`${formatCurrency(item.avgTtFee)} (${formatCurrency(
          item.avgTtFeeMin,
        )} - ${formatCurrency(item.avgTtFeeMax)})`}
      </RowText>
      <RowText>
        {`${formatCurrency(item.avgLivingExp)} (${formatCurrency(
          item.avgLivingExpMin,
        )} - ${formatCurrency(item.avgLivingExpMax)})`}
      </RowText>
    </UniversityLocationPressable>
  );
};

const Separator = styled.View(({ theme }) => ({
  width: '100%',
  height: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));

const ActionButton = styled(Button)({
  width: 200,
});

const UniversityLocationPressable = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  paddingVertical: theme.space[4],
}));

const SearchInput = styled(TextInput)(({ theme, value }) => ({
  fontFamily: 'FWDCircularTT-Medium',
  fontSize: theme.typography.largeLabel.size,
  lineHeight: theme.typography.largeLabel.lineHeight,
  height: 24,
  flex: 1,
}));

const Header = styled(Body)({ flexBasis: '33%' });

const RowText = styled(LargeBody)({ flexBasis: '33%' });

const ItemRadio = styled(RadioButton)(() => ({
  marginRight: 11,
}));

const EmptyResult = () => {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();
  return (
    <Center>
      <UniversityEmptyIcon />
      <Box mt={space[4]}>
        <LargeBody style={{ textAlign: 'center' }} color={colors.placeholder}>
          {t('fna:savingsGoals.education.search.msg1')}
        </LargeBody>
        <LargeBody style={{ textAlign: 'center' }} color={colors.placeholder}>
          {t('fna:savingsGoals.education.search.msg2')}
        </LargeBody>
      </Box>
    </Center>
  );
};
