import useBoundStore from 'hooks/useBoundStore';
import { jwtDecode } from 'jwt-decode';
import { CubeIdToken } from 'types/auth';
import { getCountry } from './misc/misc';
import { Country } from 'agent-guru';
import useCheckIsAgency from 'hooks/useCheckIsAgency';

export const useCheckIsAiBotEnabled = () => {
  const idToken = useBoundStore.getState().auth.authInfo?.idToken;

  const isAgency = useCheckIsAgency(' ');
  const isPhBanca = getCountry() === Country.PH && !isAgency;

  if (!idToken) return false;

  const { guru_enabled } = jwtDecode(String(idToken)) as CubeIdToken;

  // Not enabling for PH Banca
  return guru_enabled && !isPhBanca;
};
