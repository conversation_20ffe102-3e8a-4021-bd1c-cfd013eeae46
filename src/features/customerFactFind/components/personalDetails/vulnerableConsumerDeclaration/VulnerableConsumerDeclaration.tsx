import styled from '@emotion/native';
import { View } from 'react-native';
import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Label,
  Switch,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Control, useController } from 'react-hook-form';
import { CertificateOwnerDetailFormSchemaType } from 'features/customerFactFind/validations/certificateOwnerDetailsSchema';
import Input from 'components/Input';
import SectionContainer from '../../SectionContainer';

interface Props {
  control: Control<CertificateOwnerDetailFormSchemaType>;
}

export default function VulnerableConsumerDeclaration({ control }: Props) {
  const { space } = useTheme();
  const { t } = useTranslation(['customerFactFind']);
  const { data: optionList } = useGetOptionList();

  const {
    field: { value: isVulnerable, onChange: onChangeIsVulnerable },
  } = useController({
    name: 'isVulnerable',
    control,
  });

  const {
    field: { value, onChange: onChangeVulnerableLevel },
  } = useController({
    name: 'vulnerableLevel',
    control,
  });

  return (
    <SectionContainer
      title={t('customerFactFind:vulnerableConsumerDeclaration.title')}>
      <Content>
        <Row gap={space[3]}>
          <LargeLabel fontWeight="medium">
            {t(
              'customerFactFind:vulnerableConsumerDeclaration.isCustomerVulnerable',
            )}
          </LargeLabel>
          <Switch
            label={
              isVulnerable === 'Y'
                ? t('customerFactFind:yes')
                : t('customerFactFind:no')
            }
            checked={isVulnerable === 'Y'}
            onChange={checked => {
              onChangeIsVulnerable(checked ? 'Y' : 'N');
              if (!checked) {
                onChangeVulnerableLevel('');
              }
            }}
          />
        </Row>
        {isVulnerable === 'Y' && (
          <Column mt={space[6]} gap={space[2]}>
            <SmallLabel>
              {t(
                'customerFactFind:vulnerableConsumerDeclaration.vulnerableQuestion',
              )}
            </SmallLabel>
            <Input
              as={Picker}
              control={control}
              name="vulnerableLevel"
              type="chip"
              size="large"
              items={optionList?.VUL_CUSTOMER.options || []}
              containerStyle={{ flexWrap: 'wrap', rowGap: space[2] }}
            />
          </Column>
        )}
      </Content>
    </SectionContainer>
  );
}

const Content = styled(View)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  padding: space[6],
}));
