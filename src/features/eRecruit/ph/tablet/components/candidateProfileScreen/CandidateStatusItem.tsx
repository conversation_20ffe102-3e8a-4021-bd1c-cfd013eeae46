import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, Icon, Label, LargeLabel, Row } from 'cube-ui-components';
import ColoredText from 'features/eRecruit/ph/components/ColoredText';
import { ProgressMeterData } from 'features/eRecruit/ph/types';
import getModuleName from 'features/eRecruit/ph/utils/getModuleName';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { Pressable } from 'react-native-gesture-handler';
import { dateFormatUtil } from 'utils/helper/formatUtil';

export default function CandidateStatusItem({
  data,
  isFocused,
  onPress,
}: {
  data: ProgressMeterData;
  isFocused: boolean;
  onPress: () => void;
}) {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();

  const subItemList = data?.list ?? [];
  const isPressable = subItemList?.length > 0;
  const isCompleted = data?.isCompleted;
  const moduleName = getModuleName(data);

  const moduleCompletedDate =
    dateFormatUtil(data?.['module-completed-date']) ?? '--';
  const moduleStatus = data?.['module-status'] ?? '--';

  return (
    <Pressable onPress={onPress} disabled={!isPressable}>
      <View
        style={{
          flexDirection: isPressable ? 'row' : 'column',
          paddingVertical: space[2],
          paddingHorizontal: space[4],
          alignItems: isPressable ? 'center' : undefined,
          justifyContent: isPressable ? 'space-between' : undefined,
          backgroundColor: isFocused
            ? colors.palette.fwdGrey[20]
            : colors.background,
        }}>
        <Column gap={space[1]}>
          <Row alignItems="center" gap={space[2]}>
            <TickCircleContainer>
              <Icon.TickCircle
                fill={
                  isCompleted
                    ? colors.palette.alertGreen
                    : colors.palette.fwdGrey[100]
                }
              />
            </TickCircleContainer>
            <LargeLabel children={moduleName} />
          </Row>

          <Row gap={space[2]}>
            <TickCircleContainer />

            <Row flex={1} justifyContent="space-between">
              <Row>
                <GreyLabel children={t('status') + ': '} />
                {isCompleted ? (
                  <Label
                    fontWeight="bold"
                    color={colors.palette.alertGreen}
                    children={moduleStatus}
                  />
                ) : (
                  <ColoredText text={moduleStatus} />
                )}

                {data?.module === 'GYB' && (
                  <GreyLabel children={` (${t('optional')})`} />
                )}
              </Row>

              {!isPressable && <GreyLabel children={moduleCompletedDate} />}
            </Row>
          </Row>
        </Column>

        {isPressable && (
          <Icon.ChevronRight fill={colors.palette.fwdAlternativeOrange[100]} />
        )}
      </View>
    </Pressable>
  );
}

const TickCircleContainer = styled.View(({ theme }) => ({
  width: theme.space[6],
}));

const GreyLabel = styled(Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarker,
}));
