import React, { useState } from 'react';
import { Platform } from 'react-native';
import {
  ActionPanel,
  ActionPanelProps,
  Chip,
  Row,
  Typography,
} from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { CategoryType } from 'types/recruitmentMaterials';
import { useTranslation } from 'react-i18next';

type MaterialChipsFilterPanelProps = ActionPanelProps & {
  visible: boolean;
  setCategoryFilter: (prev: CategoryType[]) => void;
};

const CHIP_FILTERS = [
  { type: 'recruitment', label: 'materials.filter.label.recruitment' },
  { type: 'gyb', label: 'materials.filter.label.gyb' },
  { type: 'agent_to_agent', label: 'materials.filter.label.agentToAgent' },
] as const;

export default function MaterialChipsFilterPanel({
  visible,
  handleClose,
  setCategoryFilter,
}: MaterialChipsFilterPanelProps) {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { isWideScreen } = useWindowAdaptationHelpers();

  const [tempFilter, setTempFilter] = useState<CategoryType[]>([]);

  return (
    <ActionPanel
      visible={visible}
      handleClose={() => handleClose()}
      title={t('materials.filter')}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: space[isWideScreen ? 5 : 4] + bottom,
          ios: 0,
        }),
      }}>
      <Container>
        <Typography.H7
          fontWeight="bold"
          children={t('materials.filter.type')}
        />

        <Row style={{ flexWrap: 'wrap', gap: space[2] }}>
          {CHIP_FILTERS.map(({ type, label }) => (
            <Chip
              key={type}
              focus={tempFilter?.includes(type)}
              label={t(label)}
              onPress={() => {
                setTempFilter((prev: CategoryType[]) => {
                  return tempFilter?.includes(type)
                    ? prev.filter((item: CategoryType) => item !== type)
                    : [...prev, type];
                });
              }}
            />
          ))}
        </Row>
      </Container>

      <FormAction
        primaryLabel={t('materials.filter.apply')}
        onPrimaryPress={() => {
          setCategoryFilter(tempFilter);
          handleClose();
        }}
        secondaryLabel={t('materials.filter.reset')}
        onSecondaryPress={() => setTempFilter([])}
      />
    </ActionPanel>
  );
}

const Container = styled.View(({ theme }) => ({
  padding: theme.sizes[4],
  gap: theme.space[4],
}));
