import { useTheme } from '@emotion/react';
import { Box, Column, H7, H8, Icon, Row } from 'cube-ui-components';
import React from 'react';

export const OtherInsuranceQualifications = ({
  label,
  data,
}: {
  label: string;
  data: { name: string; description?: string | null }[];
}) => {
  const { space, sizes, colors } = useTheme();
  return (
    <Column gap={space[2]}>
      <H7 color={colors.palette.fwdGreyDarker}>{label}</H7>
      <Column>
        {data &&
          data.map((item, index) => {
            return (
              item && (
                <Column key={index}>
                  <Row>
                    <React.Fragment>
                      <Box marginRight={space[1]}>
                        <Icon.Tick width={sizes[5]} height={sizes[5]} />
                      </Box>
                      <Box justifyContent="flex-start" flex={2}>
                        <H8>{item?.name}</H8>
                      </Box>
                    </React.Fragment>
                  </Row>
                  <H8>{item?.description}</H8>
                </Column>
              )
            );
          })}
      </Column>
    </Column>
  );
};
