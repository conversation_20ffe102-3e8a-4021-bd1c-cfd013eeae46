import React, { useCallback, useRef, useState } from 'react';
import styled from '@emotion/native';
import { Body, Button, Column, H6, Icon, Row } from 'cube-ui-components';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import useEndLandscape from 'features/eApp/hooks/useEndLandscape';
import { useTranslation } from 'react-i18next';
import DrawPad from './DrawPad';
import DotsBar from './DotsBar';
import DeclarationModal from './DeclarationModal';

export default function SignatureSheet({
  title,
  isLoading,
  callback,
  activeIndex,
  buttonText,
  showDotsBar = true,
  signatureDesc,
  declarationTitle,
  declarationBody,
}: {
  title: string;
  isLoading: boolean;
  callback: (base64?: string) => void;
  activeIndex?: number;
  buttonText: string;
  showDotsBar?: boolean;
  signatureDesc: string;
  declarationTitle: string;
  declarationBody: string;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, sizes, colors } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const endLandscape = useEndLandscape();
  const drawPadRef = useRef<
    { capture: () => Promise<string | undefined> } | undefined
  >();
  const [strokes, setStrokes] = useState<string[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const onChangeWithnessWhiteBoard = (strokes: string[]) => {
    setStrokes(strokes);
  };
  const onClearPress = useCallback(() => {
    setStrokes([]);
  }, []);

  const PageInnerContainer = Column;
  const PadWithBtnContainer = Column;
  const CloseBtn = TouchableOpacity;
  const MoreBtn = TouchableOpacity;

  const onClick = async () => {
    const signBase64 = (await drawPadRef.current?.capture())?.replace(
      /(\r\n|\n|\r)/gm,
      '',
    );
    callback(signBase64);
  };

  const goBack = async () => {
    await endLandscape();
    navigation.goBack();
  };
  return (
    <PageContainer>
      <PageInnerContainer flex={1}>
        <TitleContainer>
          <CloseBtn onPress={goBack}>
            <Icon.Close fill={colors.secondary} size={sizes[6]} />
          </CloseBtn>
          <H6 fontWeight="bold" color={colors.palette.black}>
            {title}
          </H6>
        </TitleContainer>

        <DrawContainer>
          <PadWithBtnContainer flex={1}>
            <SubTextContainer>
              <Column flex={1}>
                <Body color={colors.palette.fwdGreyDarkest} numberOfLines={1}>
                  {t('eRecruit.application.consent.signatureStatement')}
                </Body>
              </Column>
              <MoreBtn onPress={() => setIsModalVisible(true)}>
                <Body
                  fontWeight="bold"
                  color={colors.palette.fwdAlternativeOrange[100]}>
                  {t('eRecruit.application.signature.moreDetails')}
                </Body>
              </MoreBtn>
            </SubTextContainer>
            <DrawPad
              ref={drawPadRef}
              strokes={strokes}
              onChange={onChangeWithnessWhiteBoard}
              onClearPress={onClearPress}
              signatureDesc={signatureDesc}
            />

            <BtnRow>
              <Button
                disabled={strokes.length === 0}
                text={buttonText}
                loading={isLoading}
                onPress={onClick}
                contentStyle={{
                  paddingHorizontal: space[6],
                  paddingVertical: 7,
                }}
              />
            </BtnRow>
          </PadWithBtnContainer>
          {showDotsBar && <DotsBar activeIndex={activeIndex} />}
        </DrawContainer>
      </PageInnerContainer>
      <DeclarationModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        title={declarationTitle}
        contentText={declarationBody}
      />
    </PageContainer>
  );
}

const PageContainer = styled(SafeAreaView)(({ theme: { space, colors } }) => ({
  flex: 1,
  paddingHorizontal: space[11],
  paddingBottom: space[7],
  backgroundColor: colors.palette.white,
}));

const TitleContainer = styled(Row)(({ theme: { space } }) => ({
  gap: space[4],
  padding: space[2],
  alignItems: 'center',
  marginBottom: space[1],
}));

const SubTextContainer = styled(Row)(({ theme: { space } }) => ({
  gap: space[1],
  alignItems: 'center',
  width: '100%',
  marginBottom: space[2],
}));

const DrawContainer = styled(Row)(({ theme: { space } }) => ({
  flex: 1,
  gap: space[3],
  marginTop: space[1],
}));

const BtnRow = styled(Row)(({ theme: { space, borderRadius, colors } }) => ({
  justifyContent: 'flex-end',
  marginTop: space[2],
}));
