import React from 'react';
import {
  Image,
  ImageSourcePropType,
  TouchableOpacity,
  View,
} from 'react-native';
import styled from '@emotion/native';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { Body, H6, SmallLabel } from 'cube-ui-components';
import { Spacer } from 'features/lead/ph/tablet/components/LeadTableTitleRow';
import { cheveronRightInCircle } from 'features/ecoach/assets';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const Container = styled(TouchableOpacity)<{
  bgColor: string;
  isTabletMode: boolean;
}>(({ bgColor, isTabletMode }) => ({
  flex: 1,
  flexDirection: 'column',
  justifyContent: 'space-between',
  paddingHorizontal: sizes[4],
  paddingVertical: isTabletMode ? sizes[7] : sizes[4],
  borderRadius: sizes[2],
  borderWidth: 1,
  borderColor: colors.fwdOrange[50],
  backgroundColor: bgColor,
  minHeight: isTabletMode ? 210 : 120,
}));

const TextContainer = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    flex: 1,
    width: isTabletMode ? '100%' : '80%',
  }),
);

const CardImage = styled(Image)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    position: 'absolute',
    bottom: 0,
    right: isTabletMode ? sizes[6] : sizes[3],
    width: isTabletMode ? 90 : 80,
    height: isTabletMode ? 90 : 80,
    zIndex: 1,
  }),
);

const Circle = styled(Image)<{ isTabletMode: boolean }>(({ isTabletMode }) => ({
  alignSelf: 'flex-start',
  marginTop: isTabletMode ? sizes[2] : sizes[2],
  width: sizes[8],
  height: sizes[8],
}));

const Tag = styled(View)(() => ({
  position: 'absolute',
  top: -sizes[3],
  left: sizes[3],
  width: 52,
  height: sizes[6],
  borderRadius: sizes[5],
  backgroundColor: colors.white,
  alignItems: 'center',
  justifyContent: 'center',
}));

type NavigationCardProps = {
  onPress: () => void;
  title: string;
  description: string;
  timeTag: string;
  timeDescription: string;
  imgSrc: ImageSourcePropType;
  bgColor: string;
};

const NavigationCard = ({
  title,
  description,
  timeTag,
  timeDescription,
  imgSrc,
  bgColor,
  onPress,
}: NavigationCardProps) => {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <Container isTabletMode={isTabletMode} bgColor={bgColor} onPress={onPress}>
      <Tag>
        <SmallLabel fontWeight="bold" color={colors.fwdAlternativeOrange[100]}>
          {timeTag}
        </SmallLabel>
      </Tag>
      <TextContainer isTabletMode={isTabletMode}>
        <H6 fontWeight="bold" color={colors.white}>
          {title}
        </H6>
        <Spacer height={sizes[3]} />
        <Body color={colors.white}>
          {description}
          {/*<Body color={colors.fwdYellow[100]} fontWeight="bold">*/}
          {/*  {timeDescription}*/}
          {/*</Body>*/}
        </Body>
      </TextContainer>
      {isTabletMode && (
        <Circle isTabletMode={isTabletMode} source={cheveronRightInCircle} />
      )}
      <CardImage isTabletMode={isTabletMode} source={imgSrc} />
    </Container>
  );
};

export default NavigationCard;
