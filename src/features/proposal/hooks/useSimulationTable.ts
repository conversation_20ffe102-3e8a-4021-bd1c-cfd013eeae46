import useBoundStore from 'hooks/useBoundStore';
import { t } from 'i18next';
import { useCallback, useEffect, useMemo } from 'react';
import { ProductId } from 'types/products';
import useBasePlanInfo, { useBasePlanCurrency } from './useBasePlanInfo';
import { useGetIllustrate } from './useIllustrate';

type SimulationData = {
  productId: ProductId;
};

enum QS1_PACKAGE {
  HAYAT = 'HAYAT',
  SIHAT = 'SIHAT',
  DANA = 'DANA',
  DIDIK = 'DIDIK',
}

export enum SimulationFieldMapping {
  POLICY_YEAR,
  AGE,
  SUM_ASSURED,
  BASIC_PREMIUMS,
  BASIC_PREMIUM_PAID,
  SINGLE_PREMIUMS,
  TOP_UP_PREMIUMS,
  LUMP_SUM_TOP_UP_PREMIUMS,
  PREM_EXT_BONUS,
  STARTUP_BONUS,
  FUND_ALLOCATION,
  WIT<PERSON>RAWALS,
  TOTAL_LIVING_BENEFITS,
  PROJECTED_DEATH_BENEFITS,
  ANNUALIZED_PREMIUMS,
  G<PERSON><PERSON>ANTEED_CASH_VALUE,
  BASE_PLAN_CASH_VALUE,
  SUP_BENEFIT_CASH_VALUE,
  TOTAL_CASH_VALUE,
  TOTAL_PAYOUTS,
  ANNUAL_PAYOUTS,
  GUARANTEED_PAYOUT_AMOUNT,
  MATURITY_BENEFIT,
  DEATH_BENEFIT,
  GUARANTEED_CASH_VALUE2,
  RETURN_OF_FEES,

  END_OF_CERT_YEAR,
  AGE_PERSON_COVER,
  CONTRIBUTION_PAID_EACH_YEAR,
  CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
  TOTAL_CONTRIBUTION_TO_DATE,
  TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
  TOTAL_FEES,
  TOTAL_FEES_WITH_CURRENCY,
  TOTAL_FEES_CI_FIRST,
  DEATH_BENEFIT_GUARANTEED_VALUES,
  DEATH_BENEFIT_TPD,
  GUARANTEED,

  AGE_PERSON_COVER_INVEST_FIRST,
  CONTRIBUTION_PAID_EACH_YEAR_INVEST_FIRST,
  ALLOCATED_CONTRIBUTION,
  SERVICE_WAKALAH_CHARGE_INVEST_FIRST,
  TABARRU_CHARGES,
  FUND_MANAGEMENT_WAKALAH_CHARGE,
  SUM_COVERED,
  TPD_BENEFITS,
  NON_GUARANTEED_CASH_VALUE,
  NON_GUARANTEED_DEATH_BENEFITS,

  NON_GUARANTEED,
  OFB_GUARANTEED,
  OFB_NON_GUARANTEED,
  LEGACY_GUARANTEED,
  SERVICE_WAKALAH_CHARGE,
  EN01_GUARANTEED,
  EN01_NON_GUARANTEED,
  HA2_GUARANTEED,

  AGE_BASE,
  AGE_SPOUSE,
  AGE_CHILD_1,
  AGE_CHILD_2,
  AGE_CHILD_3,
  AGE_CHILD_4,
  MAXIMUM_TOTAL_GUARANTEED_BENEFITS,
  TOTAL_FEES_FAMILY_FIRST,
  TOTAL_CONTRIBUTION_TO_DATE_WITH_SUBTITLE,

  CONTRIBUTION_PAID_CURRENCY,
  SERVICE_CHARGES,
  TABARRU_CHARGES_BASE_AND_UNIT,
  FUND_MANAGEMENT_CHARGE,
  SUM_COVERED_DEAH_AND_TPD,
  NON_GUARANTEED_DEATH_TPD_BENEFITS,
  COMMISSION,

  TOTAL_WAKALAH_FEES,
  EN3_NON_GUARANTEED,
  EN3_SURVIVAL_BENEFIT,

  CONTRIBUTION_PAID_BASE_CONTRIBUTION,
  SURRENDER_VALUES_NON_GUARANTEED_PARTICIPANTS_FUND,
  LVSGIO_DEATH_BENEFIT,
  LVSGIO_MATURITY_BENEFIT,

  EN4_DEATH_BENEFIT,
  EN6_ACCIDENT_DEATH_BENEFIT,
  EN6_NATURAL_DEATH_BENEFIT,
  FUNERAL_EXPENSE_BENEFIT,
  EN6_MATURITY_BENEFIT,

  TSI_CONTRIBUTION_PAID,
  UNALLOCATED_CONTRIBUTION_WAKALAH_FEE_WITH_CURRENCY,
  TSI_ALLOCATED_CONTRIBUTION,
  TSI_TABARRU,
  NON_GUARANTEED_DEATH_TPD_CI_BENEFIT_WITH_CURRENCY,
  DIRECT_DISTRIBUTION_COST_WITH_CURRENCY,
  IL8_CONTRIBUTION_PAID_EACH_YEAR,

  IP1_CONTRIBUTION_PAID_EACH_YEAR,
  IP1_ALLOCATED_CONTRIBUTION,
  MONTHLY_SERVICE_CHARGE,
  IP1_TABARRU_CHARGES,
  IP1_GUARANTEED_BENEFIT,
  IP1_NON_GUARANTEED_BENEFIT,
  TPD_BENEFIT_NATURAL_CAUSE_WITH_CURRENCY,
  TPD_BENEFIT_ACCIDENTAL_CAUSE_WITH_CURRENCY,
  IL8_MATURITY_BENEFIT,
  IL8_NON_GUARANTEED_CASH_VALUE,

  END_POLICY_YEAR,
  TOTAL_ANNUAL_PREM,
  TOTAL_COMMISSION_VALUE,
  TOTAL_COMMISSION_RATE,
  TOTAL_CASH_PAYMENTS,
  IB_GUARANTEED_VALUES,
  TOTAL_DEATH_BENEFIT,
  ATTAIN_AGE,

  GL_END_POLICY_YEAR,
  GL_PREMIUM_PAID,
  GL_ALLOCATED_PREMIUM,
  GL_INSURANCE_CHARGES,
  GL_BASIC_POLICY,
  GL_SCENARIO_X,
  GL_SCENARIO_Y,
  GL_UNIT_DEDUCTING_RIDER,
  GL_OTHER_CHARGES,
  GL_FUND_MANAGEMENT,
  GL_BASIC_SUM_ASSURED,
  GL_NON_GUARANTEED_INVESTMENT_ACCOUNT_VALUE,
  GL_NON_GUARANTEED_DEATH_BENEFIT,
  GL_INSURED_AGE_END,

  USP_PROJECTED_ACCOUNT_VALUE,
  USP_PROJECTED_SURRENDER_BENEFIT,
  USP_PROJECTED_DEATH_BENEFIT,

  QS1_GUARANTEED_HAYAT,
  QS1_GUARANTEED_SIHAT,
  QS1_GUARANTEED_DANA,
  QS1_GUARANTEED_DIDIK,
  LIFE_ASSURED_AGE_AT_YEAR_END,

  EN8_SURVIVAL_BENEFIT,
  EN8_GUARANTEED,
  EN8_NON_GUARANTEED,

  DIRECT_COMMISSION,
  FA1_GUARANTEED,
  TOTAL_PREMIUM_PAID,
  TOTAL_BASIC_PREMIUM_PAID,
  TOTAL_SUM_PREMIUM_PAID,
  TOTAL_FEES_LEGACY_GUARD,
  EN7_GUARANTEED,
  EN7_NON_GUARANTEED,

  CONTRIBUTION_PORTION,
  EN9_TOTAL_FEES,
  EN9_GUARANTEED,
  EN9_NON_GUARANTEED,
  TOTAL_SURRENDER_VALUE_FOR_PF,
  TOTAL_SURRENDER_VALUE_FOR_PIF,
  AT_MATURITY,
  SERVICE_CHARGE,

  WAKALAH_FEE,
  PREVAILING_SUM_COVERED,
  IP2_NON_GUARANTEED_CASH_VALUE,
  ID_POLICY_YEAR,
  ID_INSURED_AGE,
  ID_ANNUALIZED_PREMIUM,
  ID_CASH_VALUE,
  ID_LIVING_BENEFIT,
  ID_MATURITY_BENEFIT,

  HYT_GUARANTEED,
  HYT_TOTAL_FEES,
  HYT_TOTAL_CONTRIBUTION_TO_DATE,

  GUARANTEED_CASH_VALUE_AT_YEAR_END,
  END_OF_INSURANCE_PERIOD_BENEFIT,

  EMPTY_FIRST_COLUMN,
  FTP,
}

const SUFFIX = '_A';

export const EMPTY_CELL = '';

const useSimulationTable = ({ productId }: SimulationData) => {
  const basePlanInfo = useBasePlanInfo();
  const rawQuotation = useBoundStore(state => state.quotation?.rawQuotation);
  const alterations = useBoundStore(state => state.quotation?.alterations);
  const isAlteration = useMemo(
    () =>
      alterations &&
      Object.keys(alterations).length > 0 &&
      (Object.keys(alterations.withdrawal ?? {}).length > 0 ||
        Object.keys(alterations.lumpSum ?? {}).length > 0 ||
        Object.keys(alterations.premiumExtension ?? {}).length > 0),
    [alterations],
  );
  const id = useMemo(
    () => productId + (isAlteration ? SUFFIX : ''),
    [isAlteration, productId],
  );

  const currency = useBasePlanCurrency() ?? '';

  const ALL_COLUMNS = useMemo(
    () => [
      {
        title: t('proposal:simulation.policyYear'),
        children: [],
        value: SimulationFieldMapping.POLICY_YEAR,
        width: 86,
      },
      {
        title: t('proposal:simulation.age'),
        children: [],
        value: SimulationFieldMapping.AGE,
        width: 120,
      },
      {
        title: t('proposal:simulation.sumAssured'),
        children: [],
        value: SimulationFieldMapping.SUM_ASSURED,
        width: 120,
      },
      {
        title: t('proposal:simulation.basicPremiums'),
        children: [],
        value: SimulationFieldMapping.BASIC_PREMIUMS,
        width: 120,
      },
      {
        title: t('proposal:simulation.singlePremiums'),
        children: [],
        value: SimulationFieldMapping.SINGLE_PREMIUMS,
        width: 120,
      },
      {
        title: t('proposal:simulation.topUpPremiums'),
        children: [],
        value: SimulationFieldMapping.TOP_UP_PREMIUMS,
        width: 120,
      },
      {
        title: t('proposal:simulation.lumpSumTopUpPremiums'),
        children: [],
        value: SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
        width: 120,
      },
      {
        title: t('proposal:simulation.premExtensionBonus'),
        children: [],
        value: SimulationFieldMapping.PREM_EXT_BONUS,
        width: 120,
      },

      {
        title: t('proposal:simulation.startUpBonus'),
        children: [],
        value: SimulationFieldMapping.STARTUP_BONUS,
        width: 120,
      },

      {
        title: t('proposal:simulation.withdrawals'),
        children: [],
        value: SimulationFieldMapping.WITHDRAWALS,
        width: 120,
      },

      {
        title: t('proposal:simulation.amountAllocatedToFund'),
        children: [],
        value: SimulationFieldMapping.FUND_ALLOCATION,
        width: 120,
      },

      {
        title: t('proposal:simulation.totalLivingBenefits'),
        children: [{ title: '4%' }, { title: '8%' }, { title: '10%' }],
        value: SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
        width: 360,
        childrenWidth: [120, 120, 120],
      },
      {
        title: t('proposal:simulation.projectedDeathBenefits'),
        children: [{ title: '4%' }, { title: '8%' }, { title: '10%' }],
        value: SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        width: 360,
        childrenWidth: [120, 120, 120],
      },

      {
        title: t('proposal:simulation.annualizedPremiums'),
        children: [],
        value: SimulationFieldMapping.ANNUALIZED_PREMIUMS,
        width: 120,
      },

      {
        title: t('proposal:simulation.guaranteedCashValue'),
        children: [
          { title: t('proposal:simulation.withMajor') },
          { title: t('proposal:simulation.withoutMajor') },
        ],
        value: SimulationFieldMapping.GUARANTEED_CASH_VALUE,
        width: 500,
        childrenWidth: [250, 250],
      },
      {
        title: t('proposal:simulation.basePlanCashValue'),
        children: [
          { title: t('proposal:simulation.withMajor') },
          { title: t('proposal:simulation.withoutMajor') },
        ],
        value: SimulationFieldMapping.BASE_PLAN_CASH_VALUE,
        width: 500,
        childrenWidth: [250, 250],
      },

      {
        title: t('proposal:simulation.supplementaryBenefitCashValue'),
        children: [],
        value: SimulationFieldMapping.SUP_BENEFIT_CASH_VALUE,
        width: 120,
      },
      {
        title: t('proposal:simulation.totalCashValue'),
        children: [
          { title: t('proposal:simulation.withMajor') },
          { title: t('proposal:simulation.withoutMajor') },
        ],
        value: SimulationFieldMapping.TOTAL_CASH_VALUE,
        width: 500,
        childrenWidth: [250, 250],
      },
      {
        title: t('proposal:simulation.totalPayouts'),
        children: [
          { title: t('proposal:simulation.low') },
          { title: t('proposal:simulation.medium') },
          { title: t('proposal:simulation.higher') },
        ],
        value: SimulationFieldMapping.TOTAL_PAYOUTS,
        width: 360,
        childrenWidth: [120, 120, 120],
      },
      {
        title: t('proposal:simulation.annualPayout'),
        children: [],
        value: SimulationFieldMapping.ANNUAL_PAYOUTS,
        width: 120,
      },
      {
        title: t('proposal:simulation.guaranteedPayoutAmount'),
        children: [],
        value: SimulationFieldMapping.GUARANTEED_PAYOUT_AMOUNT,
        width: 120,
      },

      {
        title: t('proposal:simulation.maturityBenefit'),
        children: [],
        value: SimulationFieldMapping.MATURITY_BENEFIT,
        width: 120,
      },
      {
        title: t('proposal:simulation.deathBenefit'),
        children: [],
        value: SimulationFieldMapping.DEATH_BENEFIT,
        width: 120,
      },

      {
        title: t('proposal:simulation.guaranteedCashValue2'),
        children: [],
        value: SimulationFieldMapping.GUARANTEED_CASH_VALUE2,
        width: 120,
      },

      {
        title: t('proposal:simulation.returnOfFees'),
        children: [],
        value: SimulationFieldMapping.RETURN_OF_FEES,
        width: 120,
      },

      {
        title: t('proposal:simulation.endOfCertYear'),
        children: [],
        value: SimulationFieldMapping.END_OF_CERT_YEAR,
        width: 120,
      },

      {
        title: t('proposal:simulation.agePersonCover'),
        children: [],
        value: SimulationFieldMapping.AGE_PERSON_COVER,
        width: 120,
      },

      {
        title: t('proposal:simulation.contributionPaidEachYear', {
          currency: '',
        }),
        children: [],
        value: SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR,
        width: 120,
      },

      {
        title: t('proposal:simulation.contributionPaidEachYear', {
          currency: `(${currency})`,
        }),
        children: [],
        value: SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
        width: 120,
      },

      {
        title: t('proposal:simulation.totalContributionToDate', {
          currency: '',
        }),
        children: [],
        value: SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE,
        width: 120,
      },

      {
        title: t('proposal:simulation.totalContributionToDate', {
          currency: `(${currency})`,
        }),
        children: [],
        value: SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
        width: 120,
      },

      {
        title: t('proposal:simulation.totalFees', { currency: '' }),
        children: [
          { title: t('proposal:simulation.totalFees.managementExpenses') },
          { title: t('proposal:simulation.totalFees.totalDirectCommission') },
        ],
        value: SimulationFieldMapping.TOTAL_FEES,
        width: 500,
        childrenWidth: [250, 250],
      },

      {
        title: t('proposal:simulation.totalFees', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.totalFees.managementExpenses') },
          { title: t('proposal:simulation.totalFees.totalDirectCommission') },
        ],
        value: SimulationFieldMapping.TOTAL_FEES_WITH_CURRENCY,
        width: 500,
        childrenWidth: [250, 250],
      },

      {
        title: t('proposal:simulation.totalFees', { currency: '' }),
        children: [
          {
            title: `${t(
              'proposal:simulation.totalFees.managementExpenses',
            )} (RM)`,
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [150, 150],
          },
          {
            title: `${t(
              'proposal:simulation.totalFees.totalDirectCommission',
            )} (RM)`,
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [150, 150],
          },
        ],
        value: SimulationFieldMapping.TOTAL_FEES_CI_FIRST,
        width: 150 * 4,
        childrenWidth: [150 * 2, 150 * 2],
      },

      {
        title: t('proposal:simulation.deathBenefit'),
        children: [
          { title: t('proposal:simulation.guaranteed', { currency: '' }) },
          { title: t('proposal:simulation.nonGuaranteed', { currency: '' }) },
        ],
        value: SimulationFieldMapping.DEATH_BENEFIT_GUARANTEED_VALUES,
        width: 240,
        childrenWidth: [120, 120],
      },

      {
        title: t('proposal:simulation.deathBenefitTPD'),
        children: [
          { title: t('proposal:simulation.guaranteed') },

          {
            title: t('proposal:simulation.nonGuaranteed', { currency: '' }),
          },
        ],
        value: SimulationFieldMapping.DEATH_BENEFIT_TPD,
        width: 240,
        childrenWidth: [120, 120],
      },

      {
        title: t('proposal:simulation.guaranteed'),
        children: [{ title: t('proposal:simulation.deathBenefit') }],
        value: SimulationFieldMapping.GUARANTEED,
        width: 120,
        childrenWidth: [120],
      },

      {
        title: t('proposal:simulation.guaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.deathBenefit') },
          { title: t('proposal:simulation.tpdBenefitWithoutCurrency') },
        ],
        value: SimulationFieldMapping.HYT_GUARANTEED,
        width: 280,
        childrenWidth: [120, 160],
      },
      {
        title: t('proposal:simulation.totalFees', { currency: '' }),
        children: [
          {
            title:
              t(
                'proposal:simulation.totalFees.managementExpensesWithSubtitle',
              ) +
              '\n' +
              t('proposal:simulation.totalFees.hytRemarks'),
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [250, 250],
          },
          {
            title:
              t(
                'proposal:simulation.totalFees.totalDirectCommissionWithSubtitle',
              ) +
              '\n' +
              t('proposal:simulation.totalFees.hytRemarks'),
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [250, 250],
          },
        ],
        value: SimulationFieldMapping.HYT_TOTAL_FEES,
        width: 250 * 4,
        childrenWidth: [250 * 2, 250 * 2],
      },

      {
        title:
          t('proposal:simulation.totalContributionToDate', { currency: '' }) +
          '\n' +
          t('proposal:simulation.totalFees.hytRemarks'),
        children: [],
        value: SimulationFieldMapping.HYT_TOTAL_CONTRIBUTION_TO_DATE,
        width: 220,
      },
      {
        title: t('proposal:simulation.guaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.deathOrTPDNaturalCause') },
          { title: t('proposal:simulation.deathMosquito') },
          {
            title: t('proposal:simulation.multiIndemnityAccidentalCause'),
            children: [
              { title: t('proposal:simulation.deathOrTPD') },
              { title: t('proposal:simulation.deathOrTpdOnPublic') },
              { title: t('proposal:simulation.deathOrTpdTravelling') },
            ],
            childrenWidth: [120, 270, 180],
          },
        ],
        value: SimulationFieldMapping.OFB_GUARANTEED,
        width: 120 + 120 + 120 + 270 + 180,
        childrenWidth: [120, 120, 120 + 270 + 180],
      },

      {
        title: t('proposal:simulation.nonGuaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.surrenderValuesRequired') },
          { title: t('proposal:simulation.deathOrTPDNaturalCause') },
          { title: t('proposal:simulation.deathMosquito') },
          {
            title: t('proposal:simulation.multiIndemnityAccidentalCause'),
            children: [
              { title: t('proposal:simulation.deathOrTPD') },
              { title: t('proposal:simulation.deathOrTpdOnPublic') },
              { title: t('proposal:simulation.deathOrTpdTravelling') },
            ],
            childrenWidth: [150, 270, 180],
          },
        ],
        value: SimulationFieldMapping.OFB_NON_GUARANTEED,
        width: 1050,
        childrenWidth: [150, 150, 150, 600],
      },

      {
        title: t('proposal:simulation.guaranteed', {
          currency: '',
        }),
        children: [
          {
            title: t('proposal:simulation.cashPayments'),
            children: [{ title: `${currency}` }],
            childrenWidth: [150],
          },
          {
            title: t('proposal:simulation.totalLegacyCashPayment'),
            children: [{ title: `${currency}` }],
            childrenWidth: [150],
          },
          {
            title: t('proposal:simulation.totalCashValue'),
            children: [{ title: `${currency}` }],
            childrenWidth: [150],
          },
          {
            title: t('proposal:simulation.deathBenefit'),
            children: [{ title: `${currency}` }],
            childrenWidth: [150],
          },
          {
            title: t('proposal:simulation.additionalAccidentalDeathBenefit'),
            children: [{ title: `${currency}` }],
            childrenWidth: [150],
          },
          {
            title: t('proposal:simulation.compassionateBenefit'),
            children: [{ title: `${currency}` }],
            childrenWidth: [150],
          },
        ],
        value: SimulationFieldMapping.LEGACY_GUARANTEED,
        width: 150 * 6,
        childrenWidth: [150, 150, 150, 150, 150, 150],
      },
      {
        title: t('proposal:simulation.serviceWakalahCharge'),
        children: [],
        value: SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
        width: 160,
      },

      {
        title: t('proposal:simulation.guaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.sumCoveredTpdNaturalCause') },
          { title: t('proposal:simulation.sumCoveredTpdAccidentalCause') },
          { title: t('proposal:simulation.funeralExpenseBenefit') },
        ],
        value: SimulationFieldMapping.EN01_GUARANTEED,
        width: 220 * 3,
        childrenWidth: [220, 220, 220],
      },

      {
        title: t('proposal:simulation.nonGuaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.surrenderValuesRequired') },
          { title: t('proposal:simulation.sumCoveredTpdNaturalCause') },
          { title: t('proposal:simulation.sumCoveredTpdAccidentalCause') },
        ],
        value: SimulationFieldMapping.EN01_NON_GUARANTEED,
        width: 220 * 3,
        childrenWidth: [220, 220, 220],
      },

      {
        title: t('proposal:simulation.age'),
        children: [],
        value: SimulationFieldMapping.AGE_PERSON_COVER_INVEST_FIRST,
        width: 120,
      },

      {
        title: t('proposal:simulation.contributionPaidEachYear', {
          currency: '',
        }),
        children: [
          { title: t('proposal:simulation.baseContribution') },
          {
            title: t('proposal:simulation.topUps'),
          },
        ],
        value: SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_INVEST_FIRST,
        width: 240,
        childrenWidth: [120, 120],
      },

      {
        title: t('proposal:simulation.allocatedContribution', { currency: '' }),
        children: [
          {
            title: t('proposal:simulation.basePlan'),
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [80, 80],
          },
          {
            title: t('proposal:simulation.topUps'),
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [80, 80],
          },
          {
            title: t('proposal:simulation.totalAllocatedContribution'),
          },
        ],
        value: SimulationFieldMapping.ALLOCATED_CONTRIBUTION,
        width: 480,
        childrenWidth: [160, 160, 160],
      },

      {
        title: t('proposal:simulation.serviceWakalahChargeInvestFirst'),
        children: [],
        value: SimulationFieldMapping.SERVICE_WAKALAH_CHARGE_INVEST_FIRST,
        width: 120,
      },
      {
        title: t('proposal:simulation.tabarruCharges', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.baseCertificate'),
          },
        ],
        value: SimulationFieldMapping.TABARRU_CHARGES,
        width: 120,
        childrenWidth: [120],
      },
      {
        title: t('proposal:simulation.fundManagementWakalahCharge', {
          currency: `(${currency})`,
        }),
        children: [],
        value: SimulationFieldMapping.FUND_MANAGEMENT_WAKALAH_CHARGE,
        width: 160,
      },
      {
        title: t('proposal:simulation.sumCovered'),
        children: [],
        value: SimulationFieldMapping.SUM_COVERED,
        width: 120,
      },
      {
        title: t('proposal:simulation.tpdBenefit'),
        children: [],
        value: SimulationFieldMapping.TPD_BENEFITS,
        width: 120,
      },
      {
        title: t('proposal:simulation.nonGuaranteedCashValue', {
          currency: `(${currency})`,
        }),
        children: [],
        value: SimulationFieldMapping.NON_GUARANTEED_CASH_VALUE,
        width: 160,
      },
      {
        title: t('proposal:simulation.nonGuaranteedDeathBenefits'),
        children: [],
        value: SimulationFieldMapping.NON_GUARANTEED_DEATH_BENEFITS,
        width: 160,
      },

      {
        title: t('proposal:simulation.ageOf', {
          person: t('proposal:simulation.base'),
        }),
        children: [],
        value: SimulationFieldMapping.AGE_BASE,
        width: 60,
      },
      {
        title: t('proposal:simulation.ageOf', {
          person: t('proposal:simulation.spouse'),
        }),
        children: [],
        value: SimulationFieldMapping.AGE_SPOUSE,
        width: 72,
      },
      {
        title: t('proposal:simulation.ageOf', {
          person: `${t('proposal:simulation.child')} 1`,
        }),
        children: [],
        value: SimulationFieldMapping.AGE_CHILD_1,
        width: 75,
      },
      {
        title: t('proposal:simulation.ageOf', {
          person: `${t('proposal:simulation.child')} 2`,
        }),
        children: [],
        value: SimulationFieldMapping.AGE_CHILD_2,
        width: 75,
      },
      {
        title: t('proposal:simulation.ageOf', {
          person: `${t('proposal:simulation.child')} 3`,
        }),
        children: [],
        value: SimulationFieldMapping.AGE_CHILD_3,
        width: 75,
      },
      {
        title: t('proposal:simulation.ageOf', {
          person: `${t('proposal:simulation.child')} 4`,
        }),
        children: [],
        value: SimulationFieldMapping.AGE_CHILD_4,
        width: 75,
      },
      {
        title: t('proposal:simulation.totalContributionToDateWithSubtitle'),
        children: [],
        value: SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_SUBTITLE,
        width: 220,
      },
      {
        title: t('proposal:simulation.totalFees', { currency: '' }),
        children: [
          {
            title: t(
              'proposal:simulation.totalFees.managementExpensesWithSubtitle',
            ),
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [200, 200],
          },
          {
            title: t(
              'proposal:simulation.totalFees.totalDirectCommissionWithSubtitle',
            ),
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [200, 200],
          },
        ],
        value: SimulationFieldMapping.TOTAL_FEES_FAMILY_FIRST,
        width: 200 * 4,
        childrenWidth: [200 * 2, 200 * 2],
      },
      {
        title: t('proposal:simulation.maximumTotalGuaranteedBenefits'),
        children: [
          {
            title: t('proposal:simulation.minorCIBenefits'),
          },
          {
            title: t('proposal:simulation.majorCIBenefits'),
          },
          {
            title: t('proposal:simulation.deathBenefits'),
          },
          {
            title: t('proposal:simulation.familyCoverLimit'),
          },
        ],
        value: SimulationFieldMapping.MAXIMUM_TOTAL_GUARANTEED_BENEFITS,
        width: 880,
        childrenWidth: [220, 220, 220, 220],
      },

      {
        title: t('proposal:simulation.contributionPaid', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.baseContribution'),
          },
          {
            title: t('proposal:simulation.topUps'),
          },
        ],
        value: SimulationFieldMapping.CONTRIBUTION_PAID_CURRENCY,
        width: 160,
        childrenWidth: [80, 80],
      },

      {
        title: t('proposal:simulation.serviceCharges', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.SERVICE_CHARGES,
        width: 160,
      },
      {
        title: t('proposal:simulation.serviceCharge', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.SERVICE_CHARGE,
        width: 160,
      },
      {
        title: t('proposal:simulation.tabarruCharges', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.TABARRU_CHARGES_BASE_AND_UNIT,
        children: [
          {
            title: t('proposal:simulation.baseCertificate'),
          },
          {
            title: t('proposal:simulation.unitDeductingRider'),
          },
        ],
        width: 160,
        childrenWidth: [80, 80],
      },
      {
        title: t('proposal:simulation.fundManagementCharge', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.FUND_MANAGEMENT_CHARGE,
        width: 160,
      },
      {
        title: t('proposal:simulation.sumCovered'),
        value: SimulationFieldMapping.SUM_COVERED_DEAH_AND_TPD,
        children: [
          {
            title: t('proposal:simulation.deathBenefit'),
          },
          {
            title: t('proposal:simulation.tpdBenefit'),
          },
        ],
        width: 160,
        childrenWidth: [80, 80],
      },

      {
        title: t('proposal:simulation.nonGuaranteedDeathBenefits'),
        value: SimulationFieldMapping.NON_GUARANTEED_DEATH_TPD_BENEFITS,
        width: 160,
      },

      {
        title: t('proposal:simulation.commission'),
        value: SimulationFieldMapping.COMMISSION,
        width: 150,
        children: [{ title: currency }, { title: '%' }],
        childrenWidth: [75, 75],
      },

      {
        title: t(
          'proposal:simulation.contributionPaidWithCurrencyAndBaseContribution',
        ),
        value: SimulationFieldMapping.CONTRIBUTION_PAID_BASE_CONTRIBUTION,
        width: 160,
      },
      {
        title: t('proposal:simulation.totalWakalahFeesWithCurrency'),
        children: [
          {
            title: t('proposal:simulation.totalFees.managementExpenses'),
          },
          {
            title: t('proposal:simulation.totalFees.totalDirectCommission'),
          },
        ],
        value: SimulationFieldMapping.TOTAL_WAKALAH_FEES,
        width: 240,
        childrenWidth: [120, 120],
      },
      {
        title: t('proposal:simulation.nonGuaranteed', { currency: '' }),
        children: [
          { title: t('proposal:simulation.deathOrTPDBenefitWithCurrency') },
          { title: t('proposal:simulation.surrenderValuesWithCurrency') },
          { title: t('proposal:simulation.maturityBenefitWithCurrency') },
        ],
        value: SimulationFieldMapping.EN3_NON_GUARANTEED,
        width: 120 * 3,
        childrenWidth: [120, 120, 120],
      },
      {
        title: t('proposal:simulation.survivalBenefitWithCurrency'),
        children: [
          { title: t('proposal:simulation.guaranteedAnnualCashPayout') },
          { title: t('proposal:simulation.nonGuaranteedAnnualCashPayout') },
        ],
        value: SimulationFieldMapping.EN3_SURVIVAL_BENEFIT,
        width: 240 * 2,
        childrenWidth: [240, 240],
      },
      {
        title: t('proposal:simulation.surrenderValuesWithCurrency'),
        children: [
          { title: t('proposal:simulation.nonGuaranteedParticipantsFund') },
        ],
        value:
          SimulationFieldMapping.SURRENDER_VALUES_NON_GUARANTEED_PARTICIPANTS_FUND,
        width: 160,
        childrenWidth: [160],
      },
      {
        title: t('proposal:simulation.deathBenefitsWithCurrency'),
        children: [
          {
            title: t('proposal:simulation.guaranteedSumCovered'),
          },
          {
            title: t(
              'proposal:simulation.nonGuaranteedBenefitFromParticipantsFund',
            ),
          },
        ],
        value: SimulationFieldMapping.LVSGIO_DEATH_BENEFIT,
        width: 200 * 2,
        childrenWidth: [200, 200],
      },
      {
        title: t('proposal:simulation.maturityBenefitWithCurrency'),
        children: [
          {
            title: t('proposal:simulation.guaranteedMaturityPayout'),
          },
          {
            title: t('proposal:simulation.nonGuaranteedMaturityPayout'),
          },
        ],
        value: SimulationFieldMapping.LVSGIO_MATURITY_BENEFIT,
        width: 150 * 2,
        childrenWidth: [150, 150],
      },
      {
        title: t('proposal:simulation.deathTPDOrCIBenefitsWithCurrency'),
        children: [
          {
            title: t('proposal:simulation.guaranteedSumCovered'),
          },
          {
            title: t('proposal:simulation.nonGuaranteedSumCovered'),
          },
        ],
        value: SimulationFieldMapping.EN4_DEATH_BENEFIT,
        width: 200 * 2,
        childrenWidth: [200, 200],
      },
      {
        title: t('proposal:simulation.deathTPDBenefitDueToNaturalCause', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.guaranteedSumCoveredDeathBenefit'),
          },
          {
            title: t('proposal:simulation.guaranteedSumCoveredTPDBenefit'),
          },
          {
            title: t(
              'proposal:simulation.nonGuaranteedBenefitFromParticipantsFund',
            ),
          },
        ],
        value: SimulationFieldMapping.EN6_NATURAL_DEATH_BENEFIT,
        width: 200 * 3,
        childrenWidth: [200, 200, 200],
      },
      {
        title: t('proposal:simulation.deathTPDBenefitDueToAccidentalCause', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.guaranteedSumCoveredDeathBenefit'),
          },
          {
            title: t('proposal:simulation.guaranteedSumCoveredTPDBenefit'),
          },
          {
            title: t(
              'proposal:simulation.nonGuaranteedBenefitFromParticipantsFund',
            ),
          },
        ],
        value: SimulationFieldMapping.EN6_ACCIDENT_DEATH_BENEFIT,
        width: 200 * 3,
        childrenWidth: [200, 200, 200],
      },
      {
        title: t('proposal:simulation.funeralExpenseBenefitWithCurrency'),
        value: SimulationFieldMapping.FUNERAL_EXPENSE_BENEFIT,
        width: 160,
      },
      {
        title: t('proposal:simulation.maturityBenefitWithCurrency'),
        children: [
          {
            title: t('proposal:simulation.nonGuaranteedMaturityPayout'),
          },
        ],
        value: SimulationFieldMapping.EN6_MATURITY_BENEFIT,
        width: 150,
        childrenWidth: [150],
      },

      {
        title: t('proposal:simulation.contributionPaid', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.TSI_CONTRIBUTION_PAID,
        width: 120,
      },
      {
        title: t(
          'proposal:simulation.unallocatedContributionWakalahFeeWithCurrency',
        ),
        value:
          SimulationFieldMapping.UNALLOCATED_CONTRIBUTION_WAKALAH_FEE_WITH_CURRENCY,
        width: 240,
      },
      {
        title: t('proposal:simulation.tabarruWithCurrency'),
        children: [
          {
            title: t('proposal:simulation.baseCertificate'),
          },
        ],
        value: SimulationFieldMapping.TSI_TABARRU,
        width: 120,
        childrenWidth: [120],
      },
      {
        title: t(
          'proposal:simulation.nonGuaranteedDeathTPDCIBenefitWithCurrency',
        ),
        value:
          SimulationFieldMapping.NON_GUARANTEED_DEATH_TPD_CI_BENEFIT_WITH_CURRENCY,
        width: 240,
      },
      {
        title: t('proposal:simulation.directDistributionCostWithCurrency'),
        value: SimulationFieldMapping.DIRECT_DISTRIBUTION_COST_WITH_CURRENCY,
        width: 160,
      },
      {
        title: t('proposal:simulation.allocatedContribution', {
          currency: `(${currency})`,
        }),
        children: [{ title: currency }, { title: '%' }],
        childrenWidth: [120, 120],
        value: SimulationFieldMapping.TSI_ALLOCATED_CONTRIBUTION,
        width: 120 * 2,
      },
      {
        title: t('proposal:simulation.contributionPaidEachYear', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.baseContribution'),
          },
          {
            title: t('proposal:simulation.topUps'),
          },
        ],
        value: SimulationFieldMapping.IL8_CONTRIBUTION_PAID_EACH_YEAR,
        width: 160 * 2,
        childrenWidth: [160, 160],
      },
      {
        title: t('proposal:simulation.contributionPaidEachYear', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.basePlan'),
          },
          {
            title: t('proposal:simulation.fwdSaver'),
          },
        ],
        value: SimulationFieldMapping.IP1_CONTRIBUTION_PAID_EACH_YEAR,
        width: 160 * 2,
        childrenWidth: [160, 160],
      },
      {
        title: t('proposal:simulation.allocatedContribution', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.basePlan'),
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [80, 80],
          },
          {
            title: t('proposal:simulation.fwdSaver'),
            children: [{ title: currency }, { title: '%' }],
            childrenWidth: [80, 80],
          },
          {
            title: t('proposal:simulation.totalAllocatedContribution'),
          },
        ],
        value: SimulationFieldMapping.IP1_ALLOCATED_CONTRIBUTION,
        width: 160 * 3,
        childrenWidth: [160, 160, 160],
      },
      {
        title: t('proposal:simulation.monthlyServiceCharge', {
          currency: `(${currency})`,
        }),
        width: 120,
        value: SimulationFieldMapping.MONTHLY_SERVICE_CHARGE,
      },
      {
        title: t('proposal:simulation.tabarruCharges', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.IP1_TABARRU_CHARGES,
        width: 120,
      },
      {
        title: t('proposal:simulation.guaranteedBenefitWithCurrency'),
        value: SimulationFieldMapping.IP1_GUARANTEED_BENEFIT,
        children: [
          {
            title: t('proposal:simulation.deathOrTPDNaturalCause'),
          },
          {
            title: t('proposal:simulation.deathOrTPDAccidentalCause'),
          },
          {
            title: t(
              'proposal:simulation.deathOrTPDAccidentalCauseInPublicConveyance',
            ),
          },
          {
            title: t(
              'proposal:simulation.deathOrTPDAccidentalCauseHappenedOversea',
            ),
          },
          {
            title: t('proposal:simulation.cancer'),
          },
        ],
        width: 160 * 5,
        childrenWidth: [160, 160, 160, 160, 160],
      },
      {
        title: t('proposal:simulation.nonGuaranteedBenefitWithCurrency'),
        value: SimulationFieldMapping.IP1_NON_GUARANTEED_BENEFIT,
        children: [
          {
            title: t('proposal:simulation.fundBooster'),
          },
          {
            title: t('proposal:simulation.maturityReward'),
          },
          {
            title: t('proposal:simulation.cashValue'),
          },
          {
            title: t('proposal:simulation.deathOrTPDNaturalCause'),
          },
          {
            title: t('proposal:simulation.deathOrTPDAccidentalCause'),
          },
          {
            title: t(
              'proposal:simulation.deathOrTPDAccidentalCauseInPublicConveyance',
            ),
          },
          {
            title: t(
              'proposal:simulation.deathOrTPDAccidentalCauseHappenedOversea',
            ),
          },
        ],
        width: 160 * 7,
        childrenWidth: [160, 160, 160, 160, 160, 160, 160],
      },

      {
        title: t('proposal:simulation.tpdBenefitNaturalCauseWithCurrency'),
        value: SimulationFieldMapping.TPD_BENEFIT_NATURAL_CAUSE_WITH_CURRENCY,
        width: 160,
      },
      {
        title: t('proposal:simulation.tpdBenefitAccidentalCauseWithCurrency'),
        value:
          SimulationFieldMapping.TPD_BENEFIT_ACCIDENTAL_CAUSE_WITH_CURRENCY,
        width: 160,
      },
      {
        title: t('proposal:simulation.maturityBenefitWithCurrency'),
        value: SimulationFieldMapping.IL8_MATURITY_BENEFIT,
        width: 160,
      },
      {
        title: t('proposal:simulation.nonGuaranteedCashValue', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.cashValue'),
          },
          {
            title: t('proposal:simulation.deathBenefitNatureCause'),
          },
          {
            title: t('proposal:simulation.deathBenefitAccidentalCause'),
          },
          {
            title: t('proposal:simulation.maturityBenefit'),
          },
        ],
        value: SimulationFieldMapping.IL8_NON_GUARANTEED_CASH_VALUE,
        width: 160 * 4,
        childrenWidth: [160, 160, 160, 160],
      },
      {
        title: t('proposal:simulation.endPolicyYear'),
        value: SimulationFieldMapping.END_POLICY_YEAR,
        width: 160,
      },
      {
        title: t('proposal:simulation.totalAnnualPrem'),
        value: SimulationFieldMapping.TOTAL_ANNUAL_PREM,
        width: 160,
      },
      {
        title: t('proposal:simulation.totalCommission'),
        value: SimulationFieldMapping.TOTAL_COMMISSION_VALUE,
        width: 160 * 2,
        children: [
          {
            title: `(${currency})`,
          },
          {
            title: '(%)',
          },
        ],
        childrenWidth: [160, 160],
      },

      {
        title: t('proposal:simulation.guaranteed', { currency: '' }),
        value: SimulationFieldMapping.IB_GUARANTEED_VALUES,
        width: 160 * 3,
        children: [
          {
            title: t('proposal:simulation.totalCashPayments'),
            children: [
              {
                title: `(${currency})`,
              },
            ],
            childrenWidth: [160],
          },
          {
            title: t('proposal:simulation.totalCashValue'),
            children: [
              {
                title: `(${currency})`,
              },
            ],
            childrenWidth: [160],
          },
          {
            title: t('proposal:simulation.totalDeathBenefit'),
            children: [
              {
                title: `(${currency})`,
              },
            ],
            childrenWidth: [160],
          },
        ],
        childrenWidth: [160, 160],
      },

      {
        title: t('proposal:simulation.guaranteed', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.HA2_GUARANTEED,
        width: 160 * 2,
        children: [
          {
            title: t('proposal:simulation.cashValue'),
          },
          {
            title: t('proposal:simulation.deathBenefit'),
          },
        ],
        childrenWidth: [160, 160],
      },

      {
        title: t('proposal:simulation.attainAge'),
        value: SimulationFieldMapping.ATTAIN_AGE,
        width: 160,
      },

      {
        title: t('proposal:simulation.endPolicyYear'),
        value: SimulationFieldMapping.GL_END_POLICY_YEAR,
        width: 80,
      },

      {
        title: t('proposal:simulation.premiumPaid'),
        value: SimulationFieldMapping.GL_PREMIUM_PAID,
        width: 80,
        children: [
          {
            title: `(${currency})`,
            childrenWidth: [80],
          },
        ],
        childrenWidth: [80],
      },

      {
        title: t('proposal:simulation.allocatedPremium'),
        value: SimulationFieldMapping.GL_ALLOCATED_PREMIUM,
        width: 160 * 2,
        children: [
          {
            title: `(${currency})`,
            childrenWidth: [160],
          },
          {
            title: '(%)',
            childrenWidth: [160],
          },
        ],
        childrenWidth: [160, 160],
      },

      {
        title: t('proposal:simulation.insuranceCharges'),
        value: SimulationFieldMapping.GL_INSURANCE_CHARGES,
        width: 160 * 2,
        children: [
          {
            title: t('proposal:simulation.basicPolicy'),
            children: [
              {
                title: `(${currency})`,
              },
            ],
            childrenWidth: [160],
          },
          {
            title: t('proposal:simulation.unitDeductingRider'),
            children: [
              {
                title: `(${currency})`,
              },
            ],
            childrenWidth: [160],
          },
        ],
        childrenWidth: [160, 160],
      },

      {
        title: t('proposal:simulation.otherCharge'),
        value: SimulationFieldMapping.GL_OTHER_CHARGES,
        width: 80,
        children: [
          {
            title: `(${currency})`,
          },
        ],
        childrenWidth: [80],
      },

      {
        title: t('proposal:simulation.fundManagementCharge', { currency: '' }),
        value: SimulationFieldMapping.GL_FUND_MANAGEMENT,
        width: 160,
        children: [
          {
            title: `(${currency})`,
          },
        ],
        childrenWidth: [160],
      },

      {
        title: t('proposal:simulation.basicSumAssured'),
        value: SimulationFieldMapping.GL_BASIC_SUM_ASSURED,
        width: 160,
        children: [
          {
            title: currency,
          },
        ],
        childrenWidth: [160],
      },

      {
        title: t('proposal:simulation.nonGuaranteedInvestmentAccountValue'),
        value:
          SimulationFieldMapping.GL_NON_GUARANTEED_INVESTMENT_ACCOUNT_VALUE,
        width: 160,
        children: [
          {
            title: currency,
          },
        ],
        childrenWidth: [160],
      },

      {
        title: t('proposal:simulation.nonGuaranteedDeathBenefit'),
        value: SimulationFieldMapping.GL_NON_GUARANTEED_DEATH_BENEFIT,
        width: 160,
        children: [
          {
            title: currency,
          },
        ],
        childrenWidth: [160],
      },

      {
        title: t('proposal:simulation.commission'),
        value: SimulationFieldMapping.COMMISSION,
        width: 160,
        children: [
          {
            title: currency,
          },
          {
            title: '%',
          },
        ],
        childrenWidth: [160],
      },

      {
        title: t('proposal:simulation.insuredAgeEndofYear'),
        value: SimulationFieldMapping.GL_INSURED_AGE_END,
        width: 160,
        children: [
          {
            title: currency,
          },
        ],
        childrenWidth: [160],
      },

      {
        title: t('proposal:simulation.projectedAccountValue'),
        children: [{ title: '3.5%' }, { title: '4.0%' }, { title: '4.5%' }],
        value: SimulationFieldMapping.USP_PROJECTED_ACCOUNT_VALUE,
        width: 360,
        childrenWidth: [120, 120, 120],
      },
      {
        title: t('proposal:simulation.projectedSurrenderBenefit'),
        children: [{ title: '3.5%' }, { title: '4.0%' }, { title: '4.5%' }],
        value: SimulationFieldMapping.USP_PROJECTED_SURRENDER_BENEFIT,
        width: 360,
        childrenWidth: [120, 120, 120],
      },
      {
        title: t('proposal:simulation.projectedDeathBenefit'),
        children: [{ title: '3.5%' }, { title: '4.0%' }, { title: '4.5%' }],
        value: SimulationFieldMapping.USP_PROJECTED_DEATH_BENEFIT,
        width: 360,
        childrenWidth: [120, 120, 120],
      },

      {
        title: t('proposal:simulation.guaranteed', { currency: '' }),
        value: SimulationFieldMapping.QS1_GUARANTEED_HAYAT,
        width: 120 * 5,
        children: [
          {
            title: t('proposal:simulation.basicCashValue'),
          },
          {
            title: t('proposal:simulation.riderCashValue'),
          },
          {
            title: t('proposal:simulation.deathBenefit'),
          },
          {
            title: t('proposal:simulation.additionalADB'),
          },
          {
            title: t('proposal:simulation.tpdBenefit'),
          },
        ],
        childrenWidth: [120, 120, 120, 120, 120],
      },

      {
        title: t('proposal:simulation.guaranteed', { currency: '' }),
        value: SimulationFieldMapping.QS1_GUARANTEED_SIHAT,
        width: 120 * 4 + 160 * 3 + 200,

        children: [
          {
            title: t('proposal:simulation.basicCashValue'),
          },
          {
            title: t('proposal:simulation.riderCashValue'),
          },
          {
            title: t('proposal:simulation.deathBenefit'),
          },
          {
            title: t('proposal:simulation.additionalADB'),
          },
          {
            title: t('proposal:simulation.tpdBenefit'),
          },
          {
            title: t('proposal:simulation.additionalCIB'),
          },
          {
            title: t('proposal:simulation.hospitalIncomeBenefit'),
          },
          {
            title: t('proposal:simulation.waiverOfPremiumCIB'),
          },
        ],
        childrenWidth: [120, 120, 120, 160, 120, 160, 160, 200],
      },

      {
        title: t('proposal:simulation.guaranteed', { currency: '' }),
        value: SimulationFieldMapping.QS1_GUARANTEED_DANA,
        width: 120 * 5 + 160 * 3 + 200,
        children: [
          {
            title: t('proposal:simulation.basicCashValue'),
          },
          {
            title: t('proposal:simulation.riderCashValue'),
          },
          {
            title: t('proposal:simulation.deathBenefit'),
          },
          {
            title: t('proposal:simulation.additionalADB'),
          },
          {
            title: t('proposal:simulation.tpdBenefit'),
          },
          {
            title: t('proposal:simulation.additionalCIB'),
          },
          {
            title: t('proposal:simulation.hospitalIncomeBenefit'),
          },
          {
            title: t('proposal:simulation.savingBenefit'),
          },
          {
            title: t('proposal:simulation.waiverOfPremiumCIB'),
          },
        ],
        childrenWidth: [120, 120, 120, 160, 120, 160, 160, 120, 200],
      },

      {
        title: t('proposal:simulation.guaranteed', { currency: '' }),
        value: SimulationFieldMapping.QS1_GUARANTEED_DIDIK,
        width: 120 * 7,
        children: [
          {
            title: t('proposal:simulation.basicCashValue'),
          },
          {
            title: t('proposal:simulation.riderCashValue'),
          },
          {
            title: t('proposal:simulation.deathBenefit'),
          },
          {
            title: t('proposal:simulation.additionalADB'),
          },
          {
            title: t('proposal:simulation.tpdBenefit'),
          },
          {
            title: t('proposal:simulation.savingBenefit'),
          },
          {
            title: t('proposal:simulation.payorBenefit'),
          },
        ],
        childrenWidth: [120, 120, 120, 120, 120, 120, 120],
      },

      {
        title: "Life Assured's Age at Year End",
        value: SimulationFieldMapping.LIFE_ASSURED_AGE_AT_YEAR_END,
        width: 160,
      },
      {
        title: t('proposal:simulation.survivalBenefitWithCurrency'),
        children: [
          { title: t('proposal:simulation.nonGuaranteedAnnualCashPayout') },
        ],
        value: SimulationFieldMapping.EN8_SURVIVAL_BENEFIT,
        width: 240,
        childrenWidth: [240],
      },
      {
        title: t('proposal:simulation.guaranteed', { currency: '' }),
        children: [
          {
            title: t('proposal:simulation.sumCoveredForDeathOrTPDWithCurrency'),
          },
        ],
        value: SimulationFieldMapping.EN8_GUARANTEED,
        width: 160,
        childrenWidth: [160],
      },

      {
        title: t('proposal:simulation.guaranteed', { currency: '' }),
        children: [
          {
            title: t('proposal:simulation.sumCoveredForDeathOrTPDWithCurrency'),
          },
        ],
        value: SimulationFieldMapping.EN8_GUARANTEED,
        width: 160,
        childrenWidth: [160],
      },

      {
        title: t('proposal:simulation.basicPremiumPaid', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.BASIC_PREMIUM_PAID,
        width: 160,
      },
      {
        title: t('proposal:simulation.totalPremiumPaid', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.TOTAL_PREMIUM_PAID,
        width: 160,
      },
      {
        title: t('proposal:simulation.totalBasicPremiumPaid', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.TOTAL_BASIC_PREMIUM_PAID,
        width: 160,
      },
      {
        title: t('proposal:simulation.totalSumPremiumPaid', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.TOTAL_SUM_PREMIUM_PAID,
        width: 160,
      },
      {
        title: t('proposal:simulation.totalCommission', { currency: '' }),
        children: [
          {
            title: `(${currency})`,
          },
          {
            title: '(%)',
          },
        ],
        value: SimulationFieldMapping.TOTAL_FEES_LEGACY_GUARD,
        width: 150 * 2,
        childrenWidth: [150, 150],
      },

      {
        title: t('proposal:simulation.guaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.cashValue') },
          { title: t('proposal:simulation.deathBenefitNonAccidentalCause') },
          { title: t('proposal:simulation.deathBenefitAccidentalCause') },
          { title: t('proposal:simulation.deathBenefitPublicConveyance') },

          { title: t('proposal:simulation.deathBenefitOutsideMYS') },
          { title: t('proposal:simulation.ciBenefit') },
        ],
        value: SimulationFieldMapping.FA1_GUARANTEED,
        width: 160 * 6,
        childrenWidth: [160, 160, 160, 160, 160, 160],
      },

      {
        title: t('proposal:simulation.directCommission'),
        value: SimulationFieldMapping.DIRECT_COMMISSION,
        width: 160 * 2,
        children: [
          {
            title: `(${currency})`,
          },
          {
            title: '(%)',
          },
        ],
        childrenWidth: [160, 160],
      },
      {
        title: t('proposal:simulation.guaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.deathTPDBenefitNaturalCause') },
          { title: t('proposal:simulation.deathTPDBenefitAccidentalCause') },
          { title: t('proposal:simulation.hospitalCashBenefit') },
          { title: t('proposal:simulation.compassionateBenefit') },
        ],
        value: SimulationFieldMapping.EN7_GUARANTEED,
        width: 160 * 4,
        childrenWidth: [160, 160, 160, 160],
      },
      {
        title: t('proposal:simulation.nonGuaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.surrenderValue') },
          { title: t('proposal:simulation.deathTPDBenefitNaturalCause') },
          { title: t('proposal:simulation.deathTPDBenefitAccidentalCause') },
          { title: t('proposal:simulation.maturityBenefit') },
        ],
        value: SimulationFieldMapping.EN7_NON_GUARANTEED,
        width: 160 * 4,
        childrenWidth: [160, 160, 160, 160],
      },

      {
        title: t('proposal:simulation.contributionPortion', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.contributionPortion.base') },
          { title: t('proposal:simulation.contributionPortion.iMax') },
        ],
        value: SimulationFieldMapping.CONTRIBUTION_PORTION,
        width: 160 * 2,
        childrenWidth: [160, 160],
      },

      {
        title: t('proposal:simulation.totalFees', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.totalFees.managementExpenses'),
            children: [
              { title: t('proposal:simulation.contributionPortion.base') },
              { title: t('proposal:simulation.contributionPortion.iMax') },
            ],
            childrenWidth: [120, 120],
          },
          {
            title: t('proposal:simulation.totalFees.totalDirectCommission'),
            children: [
              { title: t('proposal:simulation.contributionPortion.base') },
              { title: t('proposal:simulation.contributionPortion.iMax') },
            ],
            childrenWidth: [120, 120],
          },
        ],
        value: SimulationFieldMapping.EN9_TOTAL_FEES,
        width: 120 * 4,
        childrenWidth: [120 * 2, 120 * 2],
      },

      {
        title: t('proposal:simulation.guaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          { title: t('proposal:simulation.sumCoveredTpdNaturalCause') },
          { title: t('proposal:simulation.sumCoveredTpdAccidentalCause') },
          { title: t('proposal:simulation.compassionateBenefit') },
          { title: t('proposal:simulation.charityBenefit') },
        ],
        value: SimulationFieldMapping.EN9_GUARANTEED,
        width: 220 * 4,
        childrenWidth: [220, 220, 220, 220],
      },
      {
        title: t('proposal:simulation.nonGuaranteed', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.annualCashPayment', {
              currency: `(${currency})`,
            }),
          },
          {
            title: t('proposal:simulation.deathTPDBenefitDueToNaturalCause', {
              currency: `(${currency})`,
            }),
          },
          {
            title: t(
              'proposal:simulation.deathTPDBenefitDueToAccidentalCause',
              {
                currency: `(${currency})`,
              },
            ),
          },
        ],
        value: SimulationFieldMapping.EN9_NON_GUARANTEED,
        width: 220 * 3,
        childrenWidth: [220, 220, 220],
      },
      {
        title: t('proposal:simulation.totalSurrenderValueForPF', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.TOTAL_SURRENDER_VALUE_FOR_PF,
        width: 160,
      },
      {
        title: t('proposal:simulation.totalSurrenderValueForPIF', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.TOTAL_SURRENDER_VALUE_FOR_PIF,
        width: 160,
      },
      {
        title: t('proposal:simulation.atMaturity', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.AT_MATURITY,
        width: 160,
      },
      {
        title: t('proposal:simulation.wakalahFee', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.WAKALAH_FEE,
        width: 160,
      },
      {
        title: t('proposal:simulation.prevailingSumCovered', {
          currency: `(${currency})`,
        }),
        value: SimulationFieldMapping.PREVAILING_SUM_COVERED,
        width: 160,
      },
      {
        title: t('proposal:simulation.nonGuaranteedCashValue', {
          currency: `(${currency})`,
        }),
        children: [
          {
            title: t('proposal:simulation.fundBooster'),
          },
          {
            title: t('proposal:simulation.accountValue'),
          },
          {
            title: t('proposal:simulation.surrenderValue'),
          },
          {
            title: t('proposal:simulation.deathBenefit'),
          },
        ],
        value: SimulationFieldMapping.IP2_NON_GUARANTEED_CASH_VALUE,
        width: 160 * 4,
        childrenWidth: [160, 160, 160, 160],
      },

      {
        title: 'Akhir Tahun Polis',
        children: [],
        value: SimulationFieldMapping.ID_POLICY_YEAR,
        width: 86,
      },

      {
        title: 'Usia',
        children: [],
        value: SimulationFieldMapping.ID_INSURED_AGE,
        width: 160,
      },
      {
        title: 'Premi Tahunan',
        children: [],
        value: SimulationFieldMapping.ID_ANNUALIZED_PREMIUM,
        width: 160,
      },
      {
        title: 'Nilai Tunai**',
        children: [],
        value: SimulationFieldMapping.ID_CASH_VALUE,
        width: 160,
      },
      {
        title: 'Mafaat Hidup',
        children: [],
        value: SimulationFieldMapping.ID_LIVING_BENEFIT,
        width: 160,
      },
      {
        title: 'Manfaat Akhir Masa Asuransi',
        children: [],
        value: SimulationFieldMapping.ID_MATURITY_BENEFIT,
        width: 160,
      },
      {
        title: t('proposal:simulation.guaranteedCashValueAtYearEnd'),
        children: [],
        value: SimulationFieldMapping.GUARANTEED_CASH_VALUE_AT_YEAR_END,
        width: 120,
      },
      {
        title: t('proposal:simulation.endOfInsurancePeriodBenefit'),
        children: [],
        value: SimulationFieldMapping.END_OF_INSURANCE_PERIOD_BENEFIT,
        width: 160,
      },
      {
        title: t('proposal:simulation.summaryOfInsuranceBenefitIllustration'),
        children: [
          {
            title: t('proposal:simulation.policyYear'),
            width: 120,
          },
          {
            title: t('proposal:simulation.insuredAge'),
            width: 160,
          },
          {
            title: t('proposal:simulation.totalPremium'),
            width: 160,
          },
          {
            title: t('proposal:simulation.cashValue'),
            width: 160,
          },
          {
            title: t('proposal:simulation.cashBenefit'),
            width: 160,
          },
          {
            title: t('proposal:simulation.accumulatedCashBenefits'),
            width: 160,
          },
          {
            title: t('proposal:simulation.deathBenefit'),
            width: 120,
          },
          {
            title: t('proposal:simulation.accidentalDeathBenefit'),
            width: 160,
          },
        ],
        value: SimulationFieldMapping.FTP,
        width: 120 + 160 + 160 + 160 + 160 + 160 + 120 + 160,
        childrenWidth: [120, 160, 160, 160, 160, 160, 120, 160],
      },
    ],
    [currency],
  );

  const {
    mutateAsync: getIllustrateData,
    isLoading,
    data: illustrateData,
  } = useGetIllustrate();

  useEffect(() => {
    if (rawQuotation) {
      if (isAlteration) {
        getIllustrateData({
          ...rawQuotation,
          alterations: {
            ...rawQuotation?.alterations,
            ...alterations,
          },
        });
      } else {
        getIllustrateData(rawQuotation);
      }
    }
  }, [rawQuotation, alterations, isAlteration]);

  const columns = useMemo(() => {
    switch (id) {
      case 'SFL':
      case 'SFL' + SUFFIX:
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.TOP_UP_PREMIUMS,
          SimulationFieldMapping.FUND_ALLOCATION,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];

      case 'MCCI':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.ANNUALIZED_PREMIUMS,
          SimulationFieldMapping.GUARANTEED_CASH_VALUE,
        ];

      case 'VBT':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.ANNUALIZED_PREMIUMS,
          SimulationFieldMapping.BASE_PLAN_CASH_VALUE,
          SimulationFieldMapping.SUP_BENEFIT_CASH_VALUE,
          SimulationFieldMapping.TOTAL_CASH_VALUE,
        ];
      case 'ICP':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.ANNUALIZED_PREMIUMS,
          SimulationFieldMapping.SUM_ASSURED,
        ];
      case 'EP':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.ANNUALIZED_PREMIUMS,
          SimulationFieldMapping.SUM_ASSURED,
        ];
      case 'STC':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.ANNUALIZED_PREMIUMS,
          SimulationFieldMapping.SUM_ASSURED,
        ];
      case 'FWDM':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.TOP_UP_PREMIUMS,
          SimulationFieldMapping.STARTUP_BONUS,
          SimulationFieldMapping.FUND_ALLOCATION,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      case 'FWDM' + SUFFIX:
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.PREM_EXT_BONUS,
          SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      case 'US02':
      case 'US02' + SUFFIX:
      case 'US09':
      case 'US09' + SUFFIX:
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.SINGLE_PREMIUMS,
          SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      case 'US11':
      case 'US11' + SUFFIX:
      case 'US13':
      case 'US13' + SUFFIX:
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.SINGLE_PREMIUMS,
          SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.TOTAL_PAYOUTS,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];

      case 'RPVLR':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.TOP_UP_PREMIUMS,
          SimulationFieldMapping.RETURN_OF_FEES,
          SimulationFieldMapping.FUND_ALLOCATION,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      case 'RPVLR' + SUFFIX:
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.RETURN_OF_FEES,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      case 'RPVLSO':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.TOP_UP_PREMIUMS,
          SimulationFieldMapping.RETURN_OF_FEES,
          SimulationFieldMapping.FUND_ALLOCATION,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      case 'RPVLSO' + SUFFIX:
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.RETURN_OF_FEES,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      // Golden 7
      case 'USP':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.SINGLE_PREMIUMS,
          SimulationFieldMapping.ANNUAL_PAYOUTS,
          SimulationFieldMapping.USP_PROJECTED_ACCOUNT_VALUE,
          SimulationFieldMapping.USP_PROJECTED_SURRENDER_BENEFIT,
          SimulationFieldMapping.USP_PROJECTED_DEATH_BENEFIT,
        ];
      case 'USP' + SUFFIX:
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.SINGLE_PREMIUMS,
          SimulationFieldMapping.ANNUAL_PAYOUTS,
          SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.USP_PROJECTED_ACCOUNT_VALUE,
          SimulationFieldMapping.USP_PROJECTED_SURRENDER_BENEFIT,
          SimulationFieldMapping.USP_PROJECTED_DEATH_BENEFIT,
        ];
      // Fast Lane
      case 'U2P':
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.STARTUP_BONUS,
          SimulationFieldMapping.TOTAL_PAYOUTS,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      case 'U2P' + SUFFIX:
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.TOTAL_PAYOUTS,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      case 'TM1':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE,
          SimulationFieldMapping.TOTAL_FEES,
          SimulationFieldMapping.DEATH_BENEFIT_GUARANTEED_VALUES,
          SimulationFieldMapping.DEATH_BENEFIT_TPD,
        ];

      case 'NGC':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_FEES_CI_FIRST,
          SimulationFieldMapping.GUARANTEED,
        ];

      // Life First
      case 'OFB':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_FEES_WITH_CURRENCY,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.OFB_GUARANTEED,
          SimulationFieldMapping.OFB_NON_GUARANTEED,
          SimulationFieldMapping.MATURITY_BENEFIT,
        ];

      // Income First
      case 'EN01':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_FEES_WITH_CURRENCY,
          SimulationFieldMapping.EN01_GUARANTEED,
          SimulationFieldMapping.EN01_NON_GUARANTEED,
        ];

      case 'ILB':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER_INVEST_FIRST,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_INVEST_FIRST,
          SimulationFieldMapping.ALLOCATED_CONTRIBUTION,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE_INVEST_FIRST,
          SimulationFieldMapping.TABARRU_CHARGES,
          SimulationFieldMapping.FUND_MANAGEMENT_WAKALAH_CHARGE,
          SimulationFieldMapping.SUM_COVERED,
          SimulationFieldMapping.TPD_BENEFITS,
          SimulationFieldMapping.NON_GUARANTEED_CASH_VALUE,
          SimulationFieldMapping.NON_GUARANTEED_DEATH_BENEFITS,
        ];
      case 'ILBP':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER_INVEST_FIRST,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_INVEST_FIRST,
          SimulationFieldMapping.ALLOCATED_CONTRIBUTION,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE_INVEST_FIRST,
          SimulationFieldMapping.TABARRU_CHARGES,
          SimulationFieldMapping.FUND_MANAGEMENT_WAKALAH_CHARGE,
        ];

      case 'FAMILYCI':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_BASE,
          SimulationFieldMapping.AGE_SPOUSE,
          SimulationFieldMapping.AGE_CHILD_1,
          SimulationFieldMapping.AGE_CHILD_2,
          SimulationFieldMapping.AGE_CHILD_3,
          SimulationFieldMapping.AGE_CHILD_4,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_SUBTITLE,
          SimulationFieldMapping.TOTAL_FEES_FAMILY_FIRST,
          SimulationFieldMapping.MAXIMUM_TOTAL_GUARANTEED_BENEFITS,
        ];

      case 'ILM':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.CONTRIBUTION_PAID_CURRENCY,
          SimulationFieldMapping.ALLOCATED_CONTRIBUTION,
          SimulationFieldMapping.SERVICE_CHARGES,
          SimulationFieldMapping.TABARRU_CHARGES_BASE_AND_UNIT,
          SimulationFieldMapping.FUND_MANAGEMENT_CHARGE,
          SimulationFieldMapping.SUM_COVERED_DEAH_AND_TPD,
          SimulationFieldMapping.NON_GUARANTEED_CASH_VALUE,
          SimulationFieldMapping.NON_GUARANTEED_DEATH_TPD_BENEFITS,
          SimulationFieldMapping.COMMISSION,
        ];
      case 'EN3':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_WAKALAH_FEES,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.GUARANTEED,
          SimulationFieldMapping.EN3_NON_GUARANTEED,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
          SimulationFieldMapping.EN3_SURVIVAL_BENEFIT,
        ];
      case 'LVSGIO':
      case 'LVS':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.CONTRIBUTION_PAID_BASE_CONTRIBUTION,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_WAKALAH_FEES,
          SimulationFieldMapping.SURRENDER_VALUES_NON_GUARANTEED_PARTICIPANTS_FUND,
          SimulationFieldMapping.LVSGIO_DEATH_BENEFIT,
          SimulationFieldMapping.LVSGIO_MATURITY_BENEFIT,
        ];
      case 'EN4':
      case 'EN5':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.CONTRIBUTION_PAID_BASE_CONTRIBUTION,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_WAKALAH_FEES,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.SURRENDER_VALUES_NON_GUARANTEED_PARTICIPANTS_FUND,
          SimulationFieldMapping.EN4_DEATH_BENEFIT,
          SimulationFieldMapping.LVSGIO_MATURITY_BENEFIT,
        ];
      case 'EN6':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_WAKALAH_FEES,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.SURRENDER_VALUES_NON_GUARANTEED_PARTICIPANTS_FUND,
          SimulationFieldMapping.EN6_NATURAL_DEATH_BENEFIT,
          SimulationFieldMapping.EN6_ACCIDENT_DEATH_BENEFIT,
          SimulationFieldMapping.FUNERAL_EXPENSE_BENEFIT,
          SimulationFieldMapping.EN6_MATURITY_BENEFIT,
        ];
      case 'EN9':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE,
          SimulationFieldMapping.CONTRIBUTION_PORTION,
          SimulationFieldMapping.SERVICE_CHARGE,
          SimulationFieldMapping.EN9_TOTAL_FEES,
          SimulationFieldMapping.IP1_TABARRU_CHARGES,
          SimulationFieldMapping.FUND_MANAGEMENT_CHARGE,
          SimulationFieldMapping.EN9_GUARANTEED,
          SimulationFieldMapping.EN9_NON_GUARANTEED,
          SimulationFieldMapping.TOTAL_SURRENDER_VALUE_FOR_PF,
          SimulationFieldMapping.TOTAL_SURRENDER_VALUE_FOR_PIF,
          SimulationFieldMapping.AT_MATURITY,
        ];
      case 'TSI':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.TSI_CONTRIBUTION_PAID,
          SimulationFieldMapping.UNALLOCATED_CONTRIBUTION_WAKALAH_FEE_WITH_CURRENCY,
          SimulationFieldMapping.TSI_ALLOCATED_CONTRIBUTION,
          SimulationFieldMapping.TSI_TABARRU,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.FUND_MANAGEMENT_WAKALAH_CHARGE,
          SimulationFieldMapping.SUM_COVERED,
          SimulationFieldMapping.NON_GUARANTEED_CASH_VALUE,
          SimulationFieldMapping.NON_GUARANTEED_DEATH_TPD_CI_BENEFIT_WITH_CURRENCY,
          SimulationFieldMapping.DIRECT_DISTRIBUTION_COST_WITH_CURRENCY,
        ];
      case 'IL8':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.IL8_CONTRIBUTION_PAID_EACH_YEAR,
          SimulationFieldMapping.ALLOCATED_CONTRIBUTION,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.TABARRU_CHARGES,
          SimulationFieldMapping.FUND_MANAGEMENT_WAKALAH_CHARGE,

          SimulationFieldMapping.SUM_COVERED,
          SimulationFieldMapping.TPD_BENEFIT_NATURAL_CAUSE_WITH_CURRENCY,
          SimulationFieldMapping.TPD_BENEFIT_ACCIDENTAL_CAUSE_WITH_CURRENCY,
          SimulationFieldMapping.IL8_MATURITY_BENEFIT,
          SimulationFieldMapping.IL8_NON_GUARANTEED_CASH_VALUE,
        ];
      case 'IL4':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.IL8_CONTRIBUTION_PAID_EACH_YEAR,
          SimulationFieldMapping.ALLOCATED_CONTRIBUTION,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.TABARRU_CHARGES,
          SimulationFieldMapping.FUND_MANAGEMENT_WAKALAH_CHARGE,
          SimulationFieldMapping.SUM_COVERED,
          SimulationFieldMapping.TPD_BENEFITS,
          SimulationFieldMapping.NON_GUARANTEED_CASH_VALUE,
          SimulationFieldMapping.NON_GUARANTEED_DEATH_BENEFITS,
        ];
      case 'IP1':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE,

          SimulationFieldMapping.IP1_CONTRIBUTION_PAID_EACH_YEAR,
          SimulationFieldMapping.IP1_ALLOCATED_CONTRIBUTION,
          SimulationFieldMapping.MONTHLY_SERVICE_CHARGE,
          SimulationFieldMapping.IP1_TABARRU_CHARGES,
          SimulationFieldMapping.FUND_MANAGEMENT_CHARGE,

          SimulationFieldMapping.SUM_COVERED,
          SimulationFieldMapping.IP1_GUARANTEED_BENEFIT,
          SimulationFieldMapping.IP1_NON_GUARANTEED_BENEFIT,
        ];
      case 'IP2':
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE,

          SimulationFieldMapping.TSI_CONTRIBUTION_PAID,
          SimulationFieldMapping.TSI_ALLOCATED_CONTRIBUTION,
          SimulationFieldMapping.WAKALAH_FEE,
          SimulationFieldMapping.MONTHLY_SERVICE_CHARGE,
          SimulationFieldMapping.IP1_TABARRU_CHARGES,
          SimulationFieldMapping.FUND_MANAGEMENT_WAKALAH_CHARGE,
          SimulationFieldMapping.PREVAILING_SUM_COVERED,
          SimulationFieldMapping.IP2_NON_GUARANTEED_CASH_VALUE,
        ];
      case 'GS1':
      case 'GS2':
        return [
          SimulationFieldMapping.END_POLICY_YEAR,
          SimulationFieldMapping.TOTAL_ANNUAL_PREM,
          SimulationFieldMapping.TOTAL_COMMISSION_VALUE,
          SimulationFieldMapping.IB_GUARANTEED_VALUES,
          SimulationFieldMapping.ATTAIN_AGE,
        ];

      case 'HA1':
        return [
          SimulationFieldMapping.END_POLICY_YEAR,
          SimulationFieldMapping.TOTAL_ANNUAL_PREM,
          SimulationFieldMapping.TOTAL_COMMISSION_VALUE,

          SimulationFieldMapping.IB_GUARANTEED_VALUES,

          SimulationFieldMapping.ATTAIN_AGE,
        ];

      case 'HA2':
        return [
          SimulationFieldMapping.END_POLICY_YEAR,
          SimulationFieldMapping.BASIC_PREMIUM_PAID,
          SimulationFieldMapping.DIRECT_COMMISSION,
          SimulationFieldMapping.HA2_GUARANTEED,
          SimulationFieldMapping.ATTAIN_AGE,
        ];

      case 'GL2':
        return [
          SimulationFieldMapping.GL_END_POLICY_YEAR,
          SimulationFieldMapping.GL_PREMIUM_PAID,
          SimulationFieldMapping.GL_ALLOCATED_PREMIUM,
          SimulationFieldMapping.GL_INSURANCE_CHARGES,
          SimulationFieldMapping.GL_OTHER_CHARGES,
          SimulationFieldMapping.GL_FUND_MANAGEMENT,
          SimulationFieldMapping.GL_BASIC_SUM_ASSURED,
          SimulationFieldMapping.GL_NON_GUARANTEED_INVESTMENT_ACCOUNT_VALUE,
          SimulationFieldMapping.GL_NON_GUARANTEED_DEATH_BENEFIT,
          SimulationFieldMapping.COMMISSION,
          SimulationFieldMapping.GL_INSURED_AGE_END,
        ];

      case 'GL1':
        return [
          SimulationFieldMapping.GL_END_POLICY_YEAR,
          SimulationFieldMapping.GL_PREMIUM_PAID,
          SimulationFieldMapping.GL_ALLOCATED_PREMIUM,
          SimulationFieldMapping.GL_INSURANCE_CHARGES,
          SimulationFieldMapping.GL_OTHER_CHARGES,
          SimulationFieldMapping.GL_FUND_MANAGEMENT,
          SimulationFieldMapping.GL_BASIC_SUM_ASSURED,
          SimulationFieldMapping.GL_NON_GUARANTEED_INVESTMENT_ACCOUNT_VALUE,
          SimulationFieldMapping.GL_NON_GUARANTEED_DEATH_BENEFIT,
          SimulationFieldMapping.COMMISSION,
          SimulationFieldMapping.GL_INSURED_AGE_END,
        ];

      case 'QS1': {
        const guaranteedColumns = {
          [QS1_PACKAGE.HAYAT]: SimulationFieldMapping.QS1_GUARANTEED_HAYAT,
          [QS1_PACKAGE.SIHAT]: SimulationFieldMapping.QS1_GUARANTEED_SIHAT,
          [QS1_PACKAGE.DANA]: SimulationFieldMapping.QS1_GUARANTEED_DANA,
          [QS1_PACKAGE.DIDIK]: SimulationFieldMapping.QS1_GUARANTEED_DIDIK,
        };

        if (basePlanInfo?.classes) {
          return [
            SimulationFieldMapping.END_POLICY_YEAR,
            guaranteedColumns[basePlanInfo?.classes as QS1_PACKAGE],
            SimulationFieldMapping.LIFE_ASSURED_AGE_AT_YEAR_END,
          ];
        } else {
          return [];
        }
      }

      case 'EN8': {
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
          SimulationFieldMapping.EN8_SURVIVAL_BENEFIT,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE,
          SimulationFieldMapping.TOTAL_WAKALAH_FEES,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.EN8_GUARANTEED,
          SimulationFieldMapping.EN3_NON_GUARANTEED,
        ];
      }

      case 'FA1': {
        return [
          SimulationFieldMapping.END_POLICY_YEAR,
          SimulationFieldMapping.BASIC_PREMIUM_PAID,
          SimulationFieldMapping.TOTAL_BASIC_PREMIUM_PAID,
          SimulationFieldMapping.DIRECT_COMMISSION,
          SimulationFieldMapping.FA1_GUARANTEED,
          SimulationFieldMapping.ATTAIN_AGE,
        ];
      }

      case 'EN7': {
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_CONTRIBUTION_TO_DATE_WITH_CURRENCY,
          SimulationFieldMapping.TOTAL_FEES_WITH_CURRENCY,
          SimulationFieldMapping.SERVICE_WAKALAH_CHARGE,
          SimulationFieldMapping.EN7_GUARANTEED,
          SimulationFieldMapping.EN7_NON_GUARANTEED,
        ];
      }

      case 'IJ7': {
        return [
          SimulationFieldMapping.END_POLICY_YEAR,
          SimulationFieldMapping.GL_PREMIUM_PAID,
          SimulationFieldMapping.GL_ALLOCATED_PREMIUM,
          SimulationFieldMapping.GL_INSURANCE_CHARGES,
          SimulationFieldMapping.GL_OTHER_CHARGES,
          SimulationFieldMapping.GL_FUND_MANAGEMENT,
          SimulationFieldMapping.GL_BASIC_SUM_ASSURED,
          SimulationFieldMapping.GL_NON_GUARANTEED_INVESTMENT_ACCOUNT_VALUE,
          SimulationFieldMapping.GL_NON_GUARANTEED_DEATH_BENEFIT,
          SimulationFieldMapping.COMMISSION,
          SimulationFieldMapping.GL_INSURED_AGE_END,
        ];
      }
      case 'LPVL' + SUFFIX:
      case 'LPVL': {
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.PREM_EXT_BONUS,
          SimulationFieldMapping.LUMP_SUM_TOP_UP_PREMIUMS,
          SimulationFieldMapping.WITHDRAWALS,
          SimulationFieldMapping.TOTAL_LIVING_BENEFITS,
          SimulationFieldMapping.PROJECTED_DEATH_BENEFITS,
        ];
      }

      case 'FWDLPR':
      case 'FWDLPJ': {
        return [
          SimulationFieldMapping.ID_POLICY_YEAR,
          SimulationFieldMapping.ID_INSURED_AGE,
          SimulationFieldMapping.ID_ANNUALIZED_PREMIUM,
          SimulationFieldMapping.ID_CASH_VALUE,
          SimulationFieldMapping.ID_LIVING_BENEFIT,
          SimulationFieldMapping.ID_MATURITY_BENEFIT,
        ];
      }

      case 'PA1': {
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.BASIC_PREMIUMS,
          SimulationFieldMapping.DIRECT_COMMISSION,
          SimulationFieldMapping.IB_GUARANTEED_VALUES,
          SimulationFieldMapping.AGE,
        ];
      }

      case 'FLP': {
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.ANNUALIZED_PREMIUMS,
          SimulationFieldMapping.GUARANTEED_CASH_VALUE_AT_YEAR_END,
        ];
      }

      case 'FCA': {
        return [
          SimulationFieldMapping.POLICY_YEAR,
          SimulationFieldMapping.AGE,
          SimulationFieldMapping.ANNUALIZED_PREMIUMS,
          SimulationFieldMapping.GUARANTEED_CASH_VALUE,
          SimulationFieldMapping.END_OF_INSURANCE_PERIOD_BENEFIT,
        ];
      }

      case 'FTP': {
        return [SimulationFieldMapping.FTP];
      }

      case 'HYT': {
        return [
          SimulationFieldMapping.END_OF_CERT_YEAR,
          SimulationFieldMapping.AGE_PERSON_COVER,
          SimulationFieldMapping.CONTRIBUTION_PAID_EACH_YEAR,
          SimulationFieldMapping.HYT_TOTAL_CONTRIBUTION_TO_DATE,
          SimulationFieldMapping.HYT_TOTAL_FEES,
          SimulationFieldMapping.HYT_GUARANTEED,
        ];
      }

      case 'FA2':
      case 'FA3': {
        return [
          SimulationFieldMapping.END_POLICY_YEAR,
          SimulationFieldMapping.TOTAL_PREMIUM_PAID,
          SimulationFieldMapping.TOTAL_SUM_PREMIUM_PAID,
          SimulationFieldMapping.TOTAL_FEES_LEGACY_GUARD,
          SimulationFieldMapping.LEGACY_GUARANTEED,
          SimulationFieldMapping.ATTAIN_AGE,
        ];
      }

      default:
        return [];
    }
  }, [basePlanInfo?.classes, id]);

  const convertDataTable = useMemo(() => {
    // Is LifeFirst
    if (illustrateData && illustrateData.quotation.illustration?.OFB) {
      const ofb = illustrateData.quotation.illustration?.OFB;
      const {
        endOfYear,
        policyAge,
        annualPrem,
        totalContPaid,
        managementExpenses,
        totalDirectCommission,
        serviceWakalahCharge,
        guaranteedNaturalCause,
        guaranteedMosquitoBorneDisease,
        guaranteedMultipleIndemnity,
        guaranteedMultipleIndemnityPublic,
        guaranteedMultipleIndemnityTravelling,
        scenario1,
        scenario2,
      } = ofb;

      switch (id) {
        case 'OFB':
          return [
            [
              endOfYear,
              policyAge,
              annualPrem,
              totalContPaid,

              // TOTAL_FEES
              managementExpenses,
              totalDirectCommission,

              serviceWakalahCharge,

              // OFB_GUARANTEED
              guaranteedNaturalCause || [],
              guaranteedMosquitoBorneDisease || [],
              guaranteedMultipleIndemnity || [],
              guaranteedMultipleIndemnityPublic || [],
              guaranteedMultipleIndemnityTravelling || [],

              // OFB_NON_GUARANTEED
              scenario1?.surrenderValue || [],
              scenario1?.noneGuaranteedNaturalCause || [],
              scenario1?.noneGuaranteedMosquitoBorneDisease || [],
              scenario1?.noneGuaranteedMultipleIndemnity || [],
              scenario1?.noneGuaranteedMultipleIndemnityPublic || [],
              scenario1?.noneGuaranteedMultipleIndemnityTravelling || [],

              scenario1?.maturityBenefit || [],
            ], // scenario 1
            [
              endOfYear,
              policyAge,
              annualPrem,
              totalContPaid,

              // TOTAL_FEES
              managementExpenses,
              totalDirectCommission,

              serviceWakalahCharge,

              // OFB_GUARANTEED
              guaranteedNaturalCause || [],
              guaranteedMosquitoBorneDisease || [],
              guaranteedMultipleIndemnity || [],
              guaranteedMultipleIndemnityPublic || [],
              guaranteedMultipleIndemnityTravelling || [],

              // OFB_NON_GUARANTEED
              scenario2?.surrenderValue || [],
              scenario2?.noneGuaranteedNaturalCause || [],
              scenario2?.noneGuaranteedMosquitoBorneDisease || [],
              scenario2?.noneGuaranteedMultipleIndemnity || [],
              scenario2?.noneGuaranteedMultipleIndemnityPublic || [],
              scenario2?.noneGuaranteedMultipleIndemnityTravelling || [],

              scenario2?.maturityBenefit || [],
            ], // scenario 2
          ];
        default:
          return [];
      }
    }

    if (illustrateData && illustrateData.quotation.illustration?.ILM) {
      const {
        endOfYear,
        policyAge,
        baseCont,
        topUpCont,
        basePrem,
        basePremPerc,
        topUp,
        topUpPerc,
        totalContPaid,
        wakalahCharge,
        tabarruCharge,
        scenario1,
        scenario2,
        sumCovered,
        guaranteedTPD,
        totalCommission,
        totalCommissionPerc,
      } = illustrateData.quotation.illustration?.ILM ?? {};
      const tabarruCharges =
        illustrateData.quotation.illustration?.summaries
          ?.totalRiderYearlyTabarruCharges;

      return [
        [
          endOfYear,

          policyAge,

          baseCont,
          topUpCont,

          basePrem,
          basePremPerc,
          topUp,
          topUpPerc,
          totalContPaid,

          wakalahCharge,

          tabarruCharge,
          tabarruCharges || [],

          scenario1.fundCharge,

          sumCovered,
          guaranteedTPD,

          scenario1.cv,
          scenario1.db,

          totalCommission,
          totalCommissionPerc,
        ],
        [
          endOfYear,

          policyAge,

          baseCont,
          topUpCont,

          basePrem,
          basePremPerc,
          topUp,
          topUpPerc,
          totalContPaid,

          wakalahCharge,

          tabarruCharge,
          tabarruCharges || [],

          scenario1.fundCharge,

          sumCovered,
          guaranteedTPD,

          scenario2.cv,
          scenario2.db,

          totalCommission,
          totalCommissionPerc,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.LVSGIO) {
      const {
        endOfYear,
        policyAge,
        annContPaid,
        totContPaid,
        totManageExpense,
        totCommissionFee,
        sumCoveredAfterLien,
        scenario1,
        scenario2,
      } = illustrateData.quotation.illustration?.LVSGIO ?? {};
      return [
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          totManageExpense,
          totCommissionFee,
          scenario1?.cv,
          sumCoveredAfterLien,
          scenario1?.db,
          [],
          [],
        ],
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          totManageExpense,
          totCommissionFee,
          scenario2?.cv,
          sumCoveredAfterLien,
          scenario2?.db,
          [],
          [],
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.LVS) {
      const {
        endOfYear,
        policyAge,
        annContPaid,
        totContPaid,
        totManageExpense,
        totCommissionFee,
        sumCoveredAfterLien,
        scenario1,
        scenario2,
      } = illustrateData.quotation.illustration?.LVS ?? {};
      return [
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          totManageExpense,
          totCommissionFee,
          scenario1?.cv,
          sumCoveredAfterLien,
          scenario1?.db,
          [],
          [],
        ],
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          totManageExpense,
          totCommissionFee,
          scenario2?.cv,
          sumCoveredAfterLien,
          scenario2?.db,
          [],
          [],
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.EN6) {
      const {
        endOfYear,
        policyAge,
        deathSumCovered,
        tpdSumCovered,
        scenario1,
        scenario2,
        accidentDeathSumCovered,
        accidentTpdSumCovered,
        funeralExp,
        annContPaid,
        totContPaid,
        annManageExpense,
        annCommissionFee,
        adminCharge,
      } = illustrateData.quotation.illustration?.EN6 ?? {};

      return [
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          annManageExpense,
          annCommissionFee,
          adminCharge,
          scenario1?.sv,
          deathSumCovered,
          tpdSumCovered,
          scenario1?.av,
          accidentDeathSumCovered,
          accidentTpdSumCovered,
          scenario1?.av,
          funeralExp,
          scenario1?.maturityBenefit,
        ],
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          annManageExpense,
          annCommissionFee,
          adminCharge,
          scenario2?.sv,
          deathSumCovered,
          tpdSumCovered,
          scenario2?.av,
          accidentDeathSumCovered,
          accidentTpdSumCovered,
          scenario2?.av,
          funeralExp,
          scenario2?.maturityBenefit,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.GL1) {
      const {
        policyYear,
        premiumPaid,
        alllocatedPrem,
        alllocatedPerc,
        policyFee,
        sumAssured,
        commission,
        commissionPerc,
        policyAge,
        scenario1,
        scenario2,
        coiRider,
        coiBasic,
      } = illustrateData.quotation.illustration?.GL1 ?? {};

      return [
        [
          policyYear,
          premiumPaid,
          alllocatedPrem,
          alllocatedPerc,
          coiBasic,
          coiRider,
          policyFee,
          scenario2.fundCharge,
          sumAssured,
          scenario2.av,
          scenario2.db,
          commission,
          commissionPerc,
          policyAge,
        ],
        [
          policyYear,
          premiumPaid,
          alllocatedPrem,
          alllocatedPerc,
          coiBasic,
          coiRider,
          policyFee,
          scenario1.fundCharge,
          sumAssured,
          scenario1.av,
          scenario1.db,
          commission,
          commissionPerc,
          policyAge,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.GL2) {
      const {
        policyYear,
        premiumPaid,
        alllocatedPrem,
        alllocatedPerc,
        policyFee,
        sumAssured,
        commission,
        commissionPerc,
        policyAge,
        scenario1,
        scenario2,
        coiRider,
      } = illustrateData.quotation.illustration?.GL2 ?? {};

      return [
        [
          policyYear,
          premiumPaid,
          alllocatedPrem,
          alllocatedPerc,
          scenario2?.coiBasic,
          (coiRider || []).map((coiRider, index) =>
            coiRider > 0 && scenario2.fundBOY[index] > 0 ? coiRider : 0,
          ),
          policyFee,
          scenario2.fundCharge,
          sumAssured,
          scenario2.av,
          scenario2.db,
          commission,
          commissionPerc,
          policyAge,
        ],
        [
          policyYear,
          premiumPaid,
          alllocatedPrem,
          alllocatedPerc,
          scenario1?.coiBasic,
          coiRider,
          policyFee,
          scenario1.fundCharge,
          sumAssured,
          scenario1.av,
          scenario1.db,
          commission,
          commissionPerc,
          policyAge,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.IJ7) {
      const {
        policyYear,
        premiumPaid,
        alllocatedPrem,
        alllocatedPerc,
        policyFee,
        sumAssured,
        commission,
        commissionPerc,
        policyAge,
        scenario1,
        scenario2,
        coiRider,
      } = illustrateData.quotation.illustration?.IJ7 ?? {};

      return [
        [
          policyYear,
          premiumPaid,
          alllocatedPrem,
          alllocatedPerc,
          scenario2?.coiBasic.map(Math.round),
          scenario2?.coiRider.map(Math.round),
          policyFee,
          scenario2?.fundCharge.map(Math.round),
          sumAssured,
          scenario2?.av,
          scenario2?.db,
          commission,
          commissionPerc,
          policyAge,
        ],
        [
          policyYear,
          premiumPaid,
          alllocatedPrem,
          alllocatedPerc,
          scenario1?.coiBasic.map(Math.round),
          scenario1?.coiRider.map(Math.round),
          policyFee,
          scenario1.fundCharge.map(Math.round),
          sumAssured,
          scenario1.av,
          scenario1.db,
          commission,
          commissionPerc,
          policyAge,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.IL8) {
      const {
        endOfYear,
        policyAge,
        baseCont,
        topUpCont,
        basePrem,
        basePremPerc,
        topUp,
        topUpPerc,
        totalContPaid,
        wakalahCharge,
        tabarruCharge,
        scenario1,
        scenario2,
        sumCovered,
        tpdBenefitNatural,
        tpdBenefitAccidental,
        maturityBenefit,
      } = illustrateData.quotation.illustration?.IL8 ?? {};

      return [
        [
          endOfYear,
          policyAge,
          baseCont,
          topUpCont,
          basePrem,
          basePremPerc,
          topUp,
          topUpPerc,
          totalContPaid,
          wakalahCharge,
          tabarruCharge,
          scenario1?.fundCharge ?? [],
          sumCovered,
          tpdBenefitNatural,
          tpdBenefitAccidental,
          maturityBenefit,
          scenario1?.cv,
          scenario1?.db,
          scenario1?.dbac,
          scenario1?.mb,
        ],
        [
          endOfYear,
          policyAge,
          baseCont,
          topUpCont,
          basePrem,
          basePremPerc,
          topUp,
          topUpPerc,
          totalContPaid,
          wakalahCharge,
          tabarruCharge,
          scenario2?.fundCharge ?? [],
          sumCovered,
          tpdBenefitNatural,
          tpdBenefitAccidental,
          maturityBenefit,
          scenario2?.cv,
          scenario2?.db,
          scenario2?.dbac,
          scenario2?.mb,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.IL4) {
      const {
        endOfYear,
        policyAge,
        baseCont,
        topUpCont,
        basePrem,
        basePremPerc,
        topUp,
        topUpPerc,
        totalContPaid,
        wakalahCharge,
        tabarruCharge,
        scenario1,
        scenario2,
        sumCovered,
        guaranteedTPD,
      } = illustrateData.quotation.illustration?.IL4 ?? {};

      return [
        [
          endOfYear,
          policyAge,
          baseCont,
          topUpCont,
          basePrem,
          basePremPerc,
          topUp,
          topUpPerc,
          totalContPaid,
          wakalahCharge,
          tabarruCharge,
          scenario1?.fundCharge,
          sumCovered,
          guaranteedTPD,
          scenario1?.cv,
          scenario1?.db,
        ],
        [
          endOfYear,
          policyAge,
          baseCont,
          topUpCont,
          basePrem,
          basePremPerc,
          topUp,
          topUpPerc,
          totalContPaid,
          wakalahCharge,
          tabarruCharge,
          scenario2?.fundCharge,
          sumCovered,
          guaranteedTPD,
          scenario2?.cv,
          scenario2?.db,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.EN8) {
      const {
        endOfYear,
        policyAge,
        annContPaid,
        scenario1,
        scenario2,
        totContPaid,
        totManageExpense,
        totCommissionFee,
        totServFee,
        sumCovered,
      } = illustrateData.quotation.illustration?.EN8 ?? {};

      return [
        [
          endOfYear,
          policyAge,
          annContPaid,
          scenario1?.cv,
          totContPaid,
          totManageExpense,
          totCommissionFee,
          totServFee,
          sumCovered,
          scenario1?.db,
          scenario1?.sv,
          scenario1?.av,
        ],
        [
          endOfYear,
          policyAge,
          annContPaid,
          scenario2?.cv,
          totContPaid,
          totManageExpense,
          totCommissionFee,
          totServFee,
          sumCovered,
          scenario2?.db,
          scenario2?.sv,
          scenario2?.av,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.EN7) {
      const {
        endOfYear,
        policyAge,
        annualPrem,
        totalContPaid,
        managementExpenses,
        totalDirectCommission,
        serviceWakalahCharge,
        guaranteedNaturalCause,
        guaranteedAccidentalCause,
        guaranteedHospitalCashBenefit,
        guaranteedCompassionateBenefit,
        scenario1,
        scenario2,
      } = illustrateData.quotation.illustration?.EN7 ?? {};

      const guaranteedHospitalCashBenefitList = Array.from(
        new Array(endOfYear.length),
      ).map((_, idx) => {
        if (idx === Math.ceil((endOfYear.length - 1) / 2))
          return guaranteedHospitalCashBenefit;

        return EMPTY_CELL;
      });

      return [
        [
          endOfYear,
          policyAge,
          annualPrem,
          totalContPaid,
          managementExpenses,
          totalDirectCommission,
          serviceWakalahCharge,
          guaranteedNaturalCause,
          guaranteedAccidentalCause,
          guaranteedHospitalCashBenefitList,
          guaranteedCompassionateBenefit,
          scenario1?.surrenderValue,
          scenario1?.nonGuaranteedNaturalCause,
          scenario1?.nonGuaranteedAccidentalCause,
          scenario1?.maturityBenefit,
        ],
        [
          endOfYear,
          policyAge,
          annualPrem,
          totalContPaid,
          managementExpenses,
          totalDirectCommission,
          serviceWakalahCharge,
          guaranteedNaturalCause,
          guaranteedAccidentalCause,
          guaranteedHospitalCashBenefitList,
          guaranteedCompassionateBenefit,
          scenario2?.surrenderValue,
          scenario2?.nonGuaranteedNaturalCause,
          scenario2?.nonGuaranteedAccidentalCause,
          scenario2?.maturityBenefit,
        ],
      ];
    }

    if (
      illustrateData &&
      illustrateData.quotation.illustration?.EN9 &&
      illustrateData.quotation.illustration?.EN9M
    ) {
      const {
        endOfYear,
        policyAge,
        annualPrem,
        totalContPaid,
        baseCont,
        adminCharge,
        managementExpenses: managementExpensesBase,
        totalDirectCommission: totalDirectCommissionBase,
        scenario1: scenario1Base,
        scenario2: scenario2Base,
        naturalDeath,
        accidentalDeath,
        compassionateBenefit,
        charityBenefit,
      } = illustrateData.quotation.illustration?.EN9 ?? {};
      const {
        cont,
        managementExpenses: managementExpensesIMax,
        totalDirectCommission: totalDirectCommissionIMax,
        scenario1: scenario1IMax,
        scenario2: scenario2IMax,
      } = illustrateData.quotation.illustration?.EN9M ?? {};

      const atMaturity = Array.from(new Array(endOfYear.length)).map(() => 0);

      return [
        [
          endOfYear,
          policyAge,
          annualPrem,
          totalContPaid,
          baseCont,
          cont,
          adminCharge,
          managementExpensesBase,
          managementExpensesIMax,
          totalDirectCommissionBase,
          totalDirectCommissionIMax,
          scenario1Base?.tabarruCharge,
          scenario1IMax?.fundCharge,
          naturalDeath,
          accidentalDeath,
          compassionateBenefit,
          charityBenefit,
          scenario1Base?.acp,
          scenario1Base?.totalNaturalDeath,
          scenario1Base?.totalAccidentalDeath,
          scenario1Base?.surrenderValue,
          scenario1IMax?.surrenderValue,
          atMaturity,
        ],
        [
          endOfYear,
          policyAge,
          annualPrem,
          totalContPaid,
          baseCont,
          cont,
          adminCharge,
          managementExpensesBase,
          managementExpensesIMax,
          totalDirectCommissionBase,
          totalDirectCommissionIMax,
          scenario2Base?.tabarruCharge,
          scenario2IMax?.fundCharge,
          naturalDeath,
          accidentalDeath,
          compassionateBenefit,
          charityBenefit,
          scenario2Base?.acp,
          scenario2Base?.totalNaturalDeath,
          scenario2Base?.totalAccidentalDeath,
          scenario2Base?.surrenderValue,
          scenario2IMax?.surrenderValue,
          atMaturity,
        ],
      ];
    }

    if (illustrateData && illustrateData.quotation.illustration?.baseIllust) {
      const baseIllust = illustrateData.quotation.illustration?.baseIllust;
      const {
        policyYear,
        policyAge,
        basePrem_A,
        lumpSum_A,
        withdrawal_A,
        fundAllocation,
        low,
        med,
        high,
        totalPrem,
        guaranteedCI,
        guaranteed,
      } = baseIllust;

      switch (id) {
        case 'SFL':
        case 'SFL' + SUFFIX:
          return [
            policyYear,
            policyAge,
            basePrem_A,
            lumpSum_A,
            withdrawal_A,
            fundAllocation,
            low.totFundValue_A,
            med.totFundValue_A,
            high.totFundValue_A,
            low.db_A,
            med.db_A,
            high.db_A,
          ];

        case 'MCCI':
          return [policyYear, policyAge, totalPrem, guaranteed, guaranteedCI];
        case 'ILB': {
          const ilb = illustrateData.quotation.illustration?.ILB;
          const {
            endOfYear,
            policyAge,
            baseCont,
            topUpCont,
            basePrem,
            basePremPerc,
            topUp,
            topUpPerc,
            totalContPaid,
            wakalahCharge,
            tabarruCharge,
            scenario1,
            scenario2,
            sumCovered,
            guaranteedTPD,
          } = ilb || {};
          return [
            [
              endOfYear,
              policyAge,
              baseCont,
              topUpCont,
              basePrem,
              basePremPerc,
              topUp,
              topUpPerc,
              totalContPaid,
              wakalahCharge,
              tabarruCharge,
              scenario1?.fundCharge,
              sumCovered,
              guaranteedTPD,
              scenario1?.cv,
              scenario1?.db,
            ],
            [
              endOfYear,
              policyAge,
              baseCont,
              topUpCont,
              basePrem,
              basePremPerc,
              topUp,
              topUpPerc,
              totalContPaid,
              wakalahCharge,
              tabarruCharge,
              scenario2?.fundCharge,
              sumCovered,
              guaranteedTPD,
              scenario2?.cv,
              scenario2?.db,
            ],
          ];
        }
        case 'ILBP': {
          const ilbp = illustrateData.quotation.illustration?.ILBP;
          const {
            endOfYear,
            policyAge,
            baseCont,
            topUpCont,
            basePrem,
            basePremPerc,
            topUp,
            topUpPerc,
            totalContPaid,
            wakalahCharge,
            tabarruCharge,
            scenario1,
            scenario2,
          } = ilbp ?? {};
          return [
            [
              endOfYear,
              policyAge,
              baseCont,
              topUpCont,
              basePrem,
              basePremPerc,
              topUp,
              topUpPerc,
              totalContPaid,
              wakalahCharge,
              tabarruCharge,
              scenario1?.fundCharge,
            ],
            [
              endOfYear,
              policyAge,
              baseCont,
              topUpCont,
              basePrem,
              basePremPerc,
              topUp,
              topUpPerc,
              totalContPaid,
              wakalahCharge,
              tabarruCharge,
              scenario2?.fundCharge,
            ],
          ];
        }
        case 'IP1': {
          const {
            endOfYear,
            policyAge,
            baseCont,
            topUpCont,
            basePrem,
            basePremPerc,
            topUp,
            topUpPerc,
            totalContPaid,
            wakalahCharge,
            scenario1: baseIllustScenario1,
            scenario2: baseIllustScenario2,
          } = baseIllust;

          const {
            sumCovered,
            tpdBenefitNatural,
            tpdBenefitAccidental,
            tpdAcipc,
            tpdAcho,
            cancer,

            fundBooster,
            maturityReward,
            scenario1,
            scenario2,
          } = illustrateData.quotation.illustration?.IP1 ?? {};

          return [
            [
              endOfYear,
              policyAge,
              baseCont,
              topUpCont,
              basePrem,
              basePremPerc,
              topUp,
              topUpPerc,
              totalContPaid,
              wakalahCharge,
              baseIllustScenario1?.tc,
              baseIllustScenario1?.fundCharge,
              sumCovered,
              tpdBenefitNatural,
              tpdBenefitAccidental,
              tpdAcipc,
              tpdAcho,
              cancer,
              fundBooster,
              maturityReward,
              scenario1?.cv,
              scenario1?.db,
              scenario1?.dbac,
              scenario1?.dbAcwipc,
              scenario1?.dbAcho,
            ],
            [
              endOfYear,
              policyAge,
              baseCont,
              topUpCont,
              basePrem,
              basePremPerc,
              topUp,
              topUpPerc,
              totalContPaid,
              wakalahCharge,
              baseIllustScenario2?.tc,
              baseIllustScenario2?.fundCharge,
              sumCovered,
              tpdBenefitNatural,
              tpdBenefitAccidental,
              tpdAcipc,
              tpdAcho,
              cancer,
              fundBooster,
              maturityReward,
              scenario2?.cv,
              scenario2?.db,
              scenario2?.dbac,
              scenario2?.dbAcwipc,
              scenario2?.dbAcho,
            ],
          ];
        }
        case 'IP2': {
          const {
            endOfYear,
            policyAge,
            totalContPaid,
            totalAllocatedCont,
            totalAllocatedContPerc,
            wakalahFee,
            wakalahCharge,
            scenario1: baseIllustScenario1,
            scenario2: baseIllustScenario2,
            sumCovered,
            fundBooster,
          } = baseIllust;

          return [
            [
              endOfYear,
              policyAge,
              totalContPaid,
              totalAllocatedCont,
              totalAllocatedContPerc,
              wakalahFee,
              wakalahCharge,
              baseIllustScenario1?.tc,
              baseIllustScenario1?.fundCharge,
              sumCovered,
              fundBooster,
              baseIllustScenario1?.av,
              baseIllustScenario1?.sv,
              baseIllustScenario1?.db,
            ],
            [
              endOfYear,
              policyAge,
              totalContPaid,
              totalAllocatedCont,
              totalAllocatedContPerc,
              wakalahFee,
              wakalahCharge,
              baseIllustScenario2?.tc,
              baseIllustScenario2?.fundCharge,
              sumCovered,
              fundBooster,
              baseIllustScenario2?.av,
              baseIllustScenario2?.sv,
              baseIllustScenario2?.db,
            ],
          ];
        }
        default:
          return [];
      }
    } else if (illustrateData && illustrateData.quotation.illustration?.VBT) {
      const vbt = illustrateData.quotation.illustration?.VBT;
      const {
        policyYear,
        policyAge,
        annualizedPremium,
        cvWm,
        cvNom,
        totalRider,
        cvWmRide,
        cvNomRide,
      } = vbt;

      return [
        policyYear,
        policyAge,
        annualizedPremium,
        cvWm,
        cvNom,
        totalRider,
        cvWmRide,
        cvNomRide,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.EP) {
      const ep = illustrateData.quotation.illustration?.EP;
      const { policyYear, policyAge, annualizedPremium, db } = ep;
      return [policyYear, policyAge, annualizedPremium, db];
    } else if (illustrateData && illustrateData.quotation.illustration?.ICP) {
      const icp = illustrateData.quotation.illustration?.ICP;
      const { policyYear, policyAge, annualizedPremium, db } = icp;
      return [policyYear, policyAge, annualizedPremium, db];
    } else if (illustrateData && illustrateData.quotation.illustration?.STC) {
      const stc = illustrateData.quotation.illustration?.STC;
      const { policyYear, policyAge, annualizedPremium, db } = stc;
      return [policyYear, policyAge, annualizedPremium, db];
    } else if (illustrateData && illustrateData.quotation.illustration?.FWDM) {
      const fwdm = illustrateData.quotation.illustration?.FWDM;
      const {
        policyYear,
        policyAge,
        premiums,
        topUp,
        welcomeBonus,
        fundAllocation,
        extendPremiums,
        bonus,
        topUpLumpSums,
        withdrawals,
        low,
        med,
        high,
      } = fwdm;
      return isAlteration
        ? [
            policyYear,
            policyAge,
            extendPremiums,
            bonus.map(Math.round),
            topUpLumpSums,
            withdrawals,
            low.fundValueEOM,
            med.fundValueEOM,
            high.fundValueEOM,
            low.dbEOM,
            med.dbEOM,
            high.dbEOM,
          ]
        : [
            policyYear,
            policyAge,
            premiums,
            topUp,
            welcomeBonus,
            fundAllocation,
            low.fundValueEOMA,
            med.fundValueEOMA,
            high.fundValueEOMA,
            low.dbEOMA,
            med.dbEOMA,
            high.dbEOMA,
          ];
    } else if (illustrateData && illustrateData.quotation.illustration?.US02) {
      const us02 = illustrateData.quotation.illustration?.US02;
      const {
        policyYear,
        policyAge,
        singlePrem,
        topUpLumpSums,
        withdrawals,
        low,
        med,
        high,
      } = us02;
      return [
        policyYear,
        policyAge,
        singlePrem,
        topUpLumpSums,
        withdrawals,
        low.fundValueEOMA,
        med.fundValueEOMA,
        high.fundValueEOMA,
        low.dbEOMA,
        med.dbEOMA,
        high.dbEOMA,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.US11) {
      const us11 = illustrateData.quotation.illustration?.US11;
      const {
        policyYear,
        policyAge,
        singlePrem,
        topUpLumpSums,
        withdrawals,
        low,
        med,
        high,
      } = us11;
      return [
        policyYear,
        policyAge,
        singlePrem,
        topUpLumpSums,
        withdrawals,
        low.payoutAmount,
        med.payoutAmount,
        high.payoutAmount,
        low.fundValueEOMA,
        med.fundValueEOMA,
        high.fundValueEOMA,
        low.dbEOMA,
        med.dbEOMA,
        high.dbEOMA,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.US09) {
      const us09 = illustrateData.quotation.illustration?.US09;
      const {
        policyYear,
        policyAge,
        singlePrem,
        fundAllocation,
        withdrawals,
        low,
        med,
        high,
      } = us09;
      return [
        policyYear,
        policyAge,
        singlePrem,
        fundAllocation,
        withdrawals,
        low.fundValueEOMA,
        med.fundValueEOMA,
        high.fundValueEOMA,
        low.dbEOMA,
        med.dbEOMA,
        high.dbEOMA,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.US13) {
      const us13 = illustrateData.quotation.illustration?.US13;
      const {
        policyYear,
        policyAge,
        singlePrem,
        topUps,
        withdrawals,
        low,
        med,
        high,
      } = us13;
      return [
        policyYear,
        policyAge,
        singlePrem,
        topUps,
        withdrawals,
        low.payoutAmount,
        med.payoutAmount,
        high.payoutAmount,
        low.fundValueEOMA,
        med.fundValueEOMA,
        high.fundValueEOMA,
        low.dbEOMA,
        med.dbEOMA,
        high.dbEOMA,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.RPVLR) {
      const rpvlr = illustrateData.quotation.illustration?.RPVLR;
      const {
        policyYear,
        policyAge,
        premiums,
        topUp,
        roc,
        fundAllocation,
        low,
        med,
        high,
        topUpLumpSums,
        withdrawals,
      } = rpvlr;

      switch (id) {
        case 'RPVLR':
          return [
            policyYear,
            policyAge,
            premiums,
            topUp,
            roc,
            fundAllocation,
            low.fundValueEOMA,
            med.fundValueEOMA,
            high.fundValueEOMA,
            low.dbEOMA,
            med.dbEOMA,
            high.dbEOMA,
          ];
        case 'RPVLR' + SUFFIX:
          return [
            policyYear,
            policyAge,
            premiums,
            topUpLumpSums,
            withdrawals,
            roc,
            low.fundValueEOMA,
            med.fundValueEOMA,
            high.fundValueEOMA,
            low.dbEOMA,
            med.dbEOMA,
            high.dbEOMA,
          ];
        default:
          return [];
      }
    } else if (
      illustrateData &&
      illustrateData.quotation.illustration?.RPVLSO
    ) {
      const rpvlso = illustrateData.quotation.illustration?.RPVLSO;
      const {
        policyYear,
        policyAge,
        premiums,
        topUp,
        roc,
        fundAllocation,
        low,
        med,
        high,
        topUpLumpSums,
        withdrawals,
      } = rpvlso;

      switch (id) {
        case 'RPVLSO':
          return [
            policyYear,
            policyAge,
            premiums,
            topUp,
            roc,
            fundAllocation,
            low.fundValueEOMA,
            med.fundValueEOMA,
            high.fundValueEOMA,
            low.dbEOMA,
            med.dbEOMA,
            high.dbEOMA,
          ];
        case 'RPVLSO' + SUFFIX:
          return [
            policyYear,
            policyAge,
            premiums,
            topUpLumpSums,
            withdrawals,
            roc,
            low.fundValueEOMA,
            med.fundValueEOMA,
            high.fundValueEOMA,
            low.dbEOMA,
            med.dbEOMA,
            high.dbEOMA,
          ];
        default:
          return [];
      }
    } else if (illustrateData && illustrateData.quotation.illustration?.USP) {
      const usp = illustrateData.quotation.illustration?.USP;
      const {
        policyYear,
        policyAge,
        singlePrem,
        annualPayouts,
        low,
        med,
        high,
        topUpLumpSums,
        withdrawals,
      } = usp;
      switch (id) {
        case 'USP':
          return [
            policyYear,
            policyAge,
            singlePrem,
            annualPayouts,
            low.projectedAccountValue,
            med.projectedAccountValue,
            high.projectedAccountValue,
            low.surrenderBenefit,
            med.surrenderBenefit,
            high.surrenderBenefit,
            low.deathBenefit,
            med.deathBenefit,
            high.deathBenefit,
          ];
        case 'USP' + SUFFIX:
          return [
            policyYear,
            policyAge,
            singlePrem,
            annualPayouts,
            topUpLumpSums,
            withdrawals,
            low.projectedAccountValue,
            med.projectedAccountValue,
            high.projectedAccountValue,
            low.surrenderBenefit,
            med.surrenderBenefit,
            high.surrenderBenefit,
            low.deathBenefit,
            med.deathBenefit,
            high.deathBenefit,
          ];
        default:
          return [];
      }
    } else if (illustrateData && illustrateData.quotation.illustration?.U2P) {
      const u2p = illustrateData.quotation.illustration?.U2P;
      const { policyYear, policyAge, newFunds, specialSI } = u2p;
      switch (id) {
        case 'U2P':
          return [
            policyYear,
            policyAge,
            newFunds?.basicPremCwa,
            newFunds?.startUpBonus,
            newFunds?.low.payoutAmountCwa,
            newFunds?.med.payoutAmountCwa,
            newFunds?.high.payoutAmountCwa,
            newFunds?.low.totLivBenefitsCwa,
            newFunds?.med.totLivBenefitsCwa,
            newFunds?.high.totLivBenefitsCwa,
            newFunds?.low.dbCwa,
            newFunds?.med.dbCwa,
            newFunds?.high.dbCwa,
          ];
        case 'U2P' + SUFFIX:
          return [
            policyYear,
            policyAge,
            specialSI?.basicPrem,
            specialSI?.topUps,
            specialSI?.withdrawals,
            specialSI?.low.payoutAmount,
            specialSI?.med.payoutAmount,
            specialSI?.high.payoutAmount,
            specialSI?.low.totLivBenefits,
            specialSI?.med.totLivBenefits,
            specialSI?.high.totLivBenefits,
            specialSI?.low.db,
            specialSI?.med.db,
            specialSI?.high.db,
          ];
        default:
          return [];
      }
    } else if (illustrateData && illustrateData.quotation.illustration?.TM1) {
      // Is Future First
      const tm1 = illustrateData.quotation.illustration?.TM1;
      const {
        policyYear,
        attainedAge,
        contributionpaideachyear,
        totalcontributionpaid,
        managementexpenses,
        cumulcommission,
        guaranteeddb,
        nonguaranteeddb,
        guaranteedtpd,
        nonguaranteedtpd,
      } = tm1;

      switch (id) {
        case 'TM1':
          return [
            policyYear,
            attainedAge,
            contributionpaideachyear,
            totalcontributionpaid,

            managementexpenses,
            cumulcommission,

            guaranteeddb,
            nonguaranteeddb,

            guaranteedtpd,
            nonguaranteedtpd,
          ];
        default:
          return [];
      }
    } else if (illustrateData && illustrateData.quotation.illustration?.NGC) {
      // Is CI First
      const ngc = illustrateData.quotation.illustration?.NGC;
      const {
        policyYear,
        attainedAge,
        temp1013,
        temp1014,
        temp1017,
        temp1016,
        deathbenefit,
        manageExpenseRate,
        directCommRate,
      } = ngc;

      switch (id) {
        case 'NGC':
          return [
            policyYear,
            attainedAge,
            (temp1013 ?? []).map(Math.round),
            temp1014,
            temp1017,
            (manageExpenseRate ?? []).map(item => item * 100),
            temp1016,
            (directCommRate ?? []).map(item => item * 100),
            deathbenefit,
          ];
        default:
          return [];
      }
    } else if (illustrateData && illustrateData.quotation.illustration?.EN01) {
      // Is CI First
      const en01 = illustrateData.quotation.illustration?.EN01;
      const { summaryTable } = en01;

      switch (id) {
        case 'EN01':
          return [
            [
              summaryTable.certYear,
              summaryTable.age,
              summaryTable.yearlyContribution,
              summaryTable.contributionPaidToDate,
              summaryTable.managementExpense,
              summaryTable.directCommission,
              // Guareented
              summaryTable.tpdNaturalCauses,
              summaryTable.tpdAccident,
              summaryTable.funeralExpense,
              // Non Guareented
              summaryTable.scenario1.surrenderValue,
              summaryTable.scenario1.sumCoveredDeathNaturalCauses,
              summaryTable.scenario1.sumCoveredDeathAccidentCauses,
            ], // scenario 1
            [
              summaryTable.certYear,
              summaryTable.age,
              summaryTable.yearlyContribution,
              summaryTable.contributionPaidToDate,
              summaryTable.managementExpense,
              summaryTable.directCommission,
              // Guareented
              summaryTable.tpdNaturalCauses,
              summaryTable.tpdAccident,
              summaryTable.funeralExpense,
              // Non Guareented
              summaryTable.scenario2.surrenderValue,
              summaryTable.scenario2.sumCoveredDeathNaturalCauses,
              summaryTable.scenario2.sumCoveredDeathAccidentCauses,
            ], // scenario 2
          ];
        default:
          return [];
      }
    } else if (
      illustrateData &&
      illustrateData.quotation.illustration?.FAMILYCI
    ) {
      const {
        policyYear,
        attainedAgePrimary,
        attainedAgeSpouse,
        attainedAgeChild1,
        attainedAgeChild2,
        attainedAgeChild3,
        attainedAgeChild4,
        contributionpaideachyear,
        totalcontributionpaid,
        managementexpenses,
        cumulcommission,
        minorcibenefit,
        majorcibenefit,
        deathbenefit,
        fccwholefamily,
        manageExpenseRate,
        directCommRate,
      } = illustrateData.quotation.illustration?.FAMILYCI ?? {};

      return [
        policyYear,
        attainedAgePrimary,
        attainedAgeSpouse,
        attainedAgeChild1,
        attainedAgeChild2,
        attainedAgeChild3,
        attainedAgeChild4,
        contributionpaideachyear,
        totalcontributionpaid,
        managementexpenses,
        (manageExpenseRate ?? []).map(item => Math.round(item * 100)),
        cumulcommission,
        (directCommRate ?? []).map(item => Math.round(item * 100)),
        minorcibenefit,
        majorcibenefit,
        deathbenefit,
        fccwholefamily,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.EN3) {
      const {
        endOfYear,
        policyAge,
        totContPaid,
        totManageExpense,
        totCommissionFee,
        totServFee,
        sumCovered,
        scenario1,
        scenario2,
        annContPaid,
      } = illustrateData.quotation.illustration?.EN3 ?? {};

      return [
        [
          endOfYear,
          policyAge,
          totContPaid,
          totManageExpense,
          totCommissionFee,
          totServFee,
          sumCovered,
          scenario1?.db,
          scenario1?.sv,
          scenario1?.av,
          annContPaid,
          [],
          scenario1?.cv,
        ],
        [
          endOfYear,
          policyAge,
          totContPaid,
          totManageExpense,
          totCommissionFee,
          totServFee,
          sumCovered,
          scenario2?.db,
          scenario2?.sv,
          scenario2?.av,
          annContPaid,
          [],
          scenario2?.cv,
        ],
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.EN4) {
      const {
        endOfYear,
        policyAge,
        annContPaid,
        totContPaid,
        annManageExpense,
        annCommissionFee,
        totServFee,
        scenario1,
        sumCovered,
        scenario2,
        maturityBenefit,
      } = illustrateData.quotation.illustration?.EN4 ?? {};

      return [
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          annManageExpense,
          annCommissionFee,
          totServFee,
          scenario1?.cv,
          sumCovered,
          scenario1?.db,
          maturityBenefit,
          [],
        ],
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          annManageExpense,
          annCommissionFee,
          totServFee,
          scenario2?.cv,
          sumCovered,
          scenario2?.db,
          maturityBenefit,
          [],
        ],
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.EN5) {
      const {
        endOfYear,
        policyAge,
        annContPaid,
        totContPaid,
        annManageExpense,
        annCommissionFee,
        totServFee,
        scenario1,
        sumCovered,
        scenario2,
        maturityBenefit,
      } = illustrateData.quotation.illustration?.EN5 ?? {};

      return [
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          annManageExpense,
          annCommissionFee,
          totServFee,
          scenario1?.cv,
          sumCovered,
          scenario1?.db,
          maturityBenefit,
          [],
        ],
        [
          endOfYear,
          policyAge,
          annContPaid,
          totContPaid,
          annManageExpense,
          annCommissionFee,
          totServFee,
          scenario2?.cv,
          sumCovered,
          scenario2?.db,
          maturityBenefit,
          [],
        ],
      ];
    } else if (
      illustrateData &&
      illustrateData.quotation.illustration?.summary?.allocatedCont
    ) {
      const {
        endOfAge,
        policyAge,
        contribution,
        wakalahFee,
        allocated,
        certificate,
        serviceFee,
        fund,
        sumCovered,
        accumulated,
        benefits,
        cost,
      } = illustrateData.quotation.illustration?.summary?.allocatedCont ?? {};

      return [
        [
          endOfAge,
          policyAge,
          contribution,
          wakalahFee,
          allocated?.allocatedPrem ?? [],
          allocated?.allocatesPremPerc ?? [],
          certificate?.baseSceA ?? [],
          serviceFee,
          fund?.fundSceA ?? [],
          sumCovered,
          accumulated?.accumSceA ?? [],
          benefits?.benefitScceA ?? [],
          cost,
        ],
        [
          endOfAge,
          policyAge,
          contribution,
          wakalahFee,
          allocated?.allocatedPrem ?? [],
          allocated?.allocatesPremPerc ?? [],
          certificate?.baseSceB ?? [],
          serviceFee,
          fund?.fundSceB ?? [],
          sumCovered,
          accumulated?.accumSceB ?? [],
          benefits?.benefitScceB ?? [],
          cost,
        ],
      ];
    } else if (
      illustrateData &&
      (illustrateData.quotation.illustration?.GS1 ||
        illustrateData.quotation.illustration?.GS2)
    ) {
      const {
        endPolicyYear,
        totalAnnualPrem,
        totalCommissionValue,
        totalCommissionRate,
        totalCashPayment,
        totalCashValue,
        totalDeathBenefit,
        attainAge,
      } = illustrateData.quotation.illustration?.summaries ?? {};

      return [
        endPolicyYear,
        totalAnnualPrem,
        totalCommissionValue,
        (totalCommissionRate || []).map(item => Math.round(item * 100)),
        totalCashPayment,
        totalCashValue,
        totalDeathBenefit,
        attainAge,
      ];
    } else if (
      illustrateData &&
      (illustrateData.quotation.illustration?.FA2 ||
        illustrateData.quotation.illustration?.FA3)
    ) {
      const {
        endPolicyYear,
        totalPremiumPaid,
        totalSumPremiumPaid,
        totalCommissionValue,
        totalCommissionRate,
        totalCashPayment,
        totalLegacyCashPayment,
        totalCashValue,
        totalDeathBenefit,
        totalAdditionalAccidentalDeathBenefit,
        totalCompassionateBenefit,
        endPolicyAge,
      } = illustrateData.quotation.illustration?.summaries ?? {};

      return [
        endPolicyYear,
        totalPremiumPaid,
        totalSumPremiumPaid,
        totalCommissionValue,
        totalCommissionRate,
        totalCashPayment,
        totalLegacyCashPayment,
        totalCashValue,
        totalDeathBenefit,
        totalAdditionalAccidentalDeathBenefit,
        totalCompassionateBenefit,
        endPolicyAge,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.HA1) {
      const {
        policyYear,
        totalAnnualPrem,
        totalCommissionValue,
        totalCommissionRate,
        totalCashPayment,
        totalCashValue,
        totalDeathBenefit,
        policyAge,
      } = illustrateData.quotation.illustration?.summaries ?? {};

      return [
        policyYear,
        totalAnnualPrem,
        totalCommissionValue,
        (totalCommissionRate ?? []).map(item => Math.round(item * 100)),
        totalCashPayment,
        totalCashValue,
        totalDeathBenefit,
        policyAge,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.HA2) {
      const {
        policyAge,
        policyYear,
        premiumPaid,
        commissionValue,
        commissionRate,
        cashValue,
        deathBenefit,
      } = illustrateData.quotation.illustration?.HA2 ?? {};

      return [
        policyYear,
        premiumPaid,
        commissionValue,
        commissionRate,
        cashValue,
        deathBenefit,
        policyAge,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.QS1) {
      const { totalRiderCashValues } =
        illustrateData.quotation.illustration?.summaries ?? {};
      const {
        endPolicyYear,
        cashValue,
        deathBenefit,
        additionalAccidentalDeathBenefit,
        tpdBenefit,
        lifeAssuredAgeAtYearEnd,
      } = illustrateData.quotation.illustration?.QS1 ?? {};

      const qca1Benefit = illustrateData.quotation.illustration?.QCA1?.benefit;

      const qhi1Benefit = illustrateData.quotation.illustration?.QHI1?.benefit;

      const qwc1Benefit = illustrateData.quotation.illustration?.QWC1?.benefit;

      const qsb1Benefit = illustrateData.quotation.illustration?.QSB1?.benefit;

      const qpb1Benefit = illustrateData.quotation.illustration?.QPB1?.benefit;

      switch (basePlanInfo?.classes) {
        case QS1_PACKAGE.HAYAT:
          return [
            endPolicyYear,
            cashValue,
            totalRiderCashValues,
            deathBenefit,
            additionalAccidentalDeathBenefit,
            tpdBenefit,
            lifeAssuredAgeAtYearEnd,
          ];

        case QS1_PACKAGE.SIHAT:
          return [
            endPolicyYear,
            cashValue,
            totalRiderCashValues,
            deathBenefit,
            additionalAccidentalDeathBenefit,
            tpdBenefit,
            qca1Benefit,
            qhi1Benefit,
            qwc1Benefit,
            lifeAssuredAgeAtYearEnd,
          ];

        case QS1_PACKAGE.DANA:
          return [
            endPolicyYear,
            cashValue,
            totalRiderCashValues,
            deathBenefit,
            additionalAccidentalDeathBenefit,
            tpdBenefit,
            qca1Benefit,
            qhi1Benefit,
            qsb1Benefit,
            qwc1Benefit,
            lifeAssuredAgeAtYearEnd,
          ];

        case QS1_PACKAGE.DIDIK:
          return [
            endPolicyYear,
            cashValue,
            totalRiderCashValues,
            deathBenefit,
            additionalAccidentalDeathBenefit,
            tpdBenefit,
            qsb1Benefit,
            qpb1Benefit,
            lifeAssuredAgeAtYearEnd,
          ];
      }
    } else if (illustrateData && illustrateData.quotation.illustration?.FA1) {
      const {
        policyYear,
        premiumPaid,
        commissionValue,
        commissionRate,
        cashValue,
        nonAccidentalCauses,
        accidentalCauses,
        publicConveyance,
        outsideMalaysia,
        ciBenefit,
        policyAge,
        totalPremiumPaid,
      } = illustrateData.quotation.illustration?.FA1 ?? {};

      return [
        policyYear,
        premiumPaid,
        totalPremiumPaid,
        commissionValue,
        commissionRate,
        cashValue,
        nonAccidentalCauses,
        accidentalCauses,
        publicConveyance,
        outsideMalaysia,
        ciBenefit,
        policyAge,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.LPVL) {
      const {
        policyYear,
        policyAge,
        extendPremiums,
        bonus,
        topUpLumpSums,
        withdrawals,
        low,
        med,
        high,
      } = illustrateData.quotation.illustration?.LPVL ?? {};

      return [
        policyYear,
        policyAge,
        extendPremiums,
        bonus.map(Math.round),
        topUpLumpSums,
        withdrawals,
        low?.fundValueEOMA ?? [],
        med?.fundValueEOMA ?? [],
        high?.fundValueEOMA ?? [],
        low?.dbEOMA ?? [],
        med?.dbEOMA ?? [],
        high?.dbEOMA ?? [],
      ];
    } else if (
      illustrateData &&
      (illustrateData.quotation.illustration?.FWDLPR ||
        illustrateData.quotation.illustration?.FWDLPJ)
    ) {
      const {
        policyYear,
        insuredAge,
        annualizedPremium,
        cashValue,
        livingBenefit,
        maturityBenefit,
      } =
        illustrateData.quotation.illustration?.FWDLPR ??
        illustrateData.quotation.illustration?.FWDLPJ ??
        {};

      return [
        policyYear,
        insuredAge,
        annualizedPremium,
        cashValue,
        livingBenefit,
        maturityBenefit,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.PA1) {
      const {
        policyYear,
        premiumPaid,
        commissionValue,
        commissionRate,
        cashPayment,
        cashValue,
        deathBenefit,
        policyAge,
      } = illustrateData.quotation.illustration?.PA1 ?? {};

      return [
        policyYear,
        premiumPaid,
        commissionValue,
        commissionRate,
        cashPayment,
        cashValue,
        deathBenefit,
        policyAge,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.FLP) {
      const { policyYear, policyAge, annualizedPremium, cvPrem } =
        illustrateData.quotation.illustration?.FLP ?? {};

      return [policyYear, policyAge, annualizedPremium, cvPrem];
    } else if (illustrateData && illustrateData.quotation.illustration?.FCA) {
      const { policyYear, age, annualizedPrem, withMaCI, withoutMaCI, mab } =
        illustrateData.quotation.illustration?.FCA ?? {};

      return [policyYear, age, annualizedPrem, withMaCI, withoutMaCI, mab];
    } else if (illustrateData && illustrateData.quotation.illustration?.FTP) {
      const {
        policyYear,
        insuredAge,
        totalAnnualizedPremium,
        surrenderBenefit,
        maturityBenefit,
        accumulatedMaturityBenefit,
        deathBenefit,
        totalAccidentalDeathBenefit,
      } = illustrateData.quotation.illustration?.FTP ?? {};

      return [
        policyYear,
        insuredAge,
        totalAnnualizedPremium,
        surrenderBenefit,
        maturityBenefit,
        accumulatedMaturityBenefit,
        deathBenefit,
        totalAccidentalDeathBenefit,
      ];
    } else if (illustrateData && illustrateData.quotation.illustration?.HYT) {
      const {
        endOfYear,
        policyAge,
        annualPrem,
        totalContPaid,
        managementExpenses,
        managementExpensesPerc,
        totalDirectCommission,
        totalDirectCommissionPerc,
        deathBenefit,
        tpdBenefit,
      } = illustrateData.quotation.illustration?.HYT ?? {};

      return [
        endOfYear,
        policyAge,
        annualPrem,
        totalContPaid,
        managementExpenses,
        decimalToPercentageStr(managementExpensesPerc),
        totalDirectCommission,
        decimalToPercentageStr(totalDirectCommissionPerc),
        deathBenefit,
        tpdBenefit,
      ];
    }
  }, [illustrateData, id, isAlteration, basePlanInfo?.classes]);

  /**
   * This function used to return a flat array of children widths of multi-subHeaders
   * @param {typeof ALL_COLUMNS} headerColumns    Array of headerData, define columns
   * @param {number[]} prev                       Previous calculated widths, used to handle multi-subheaders
   *
   * @returns {number[]} list of flatted widths
   */
  const flatChildColumnWidthRows = useCallback(
    (headerColumns: typeof ALL_COLUMNS): number[] => {
      let widths: number[] = [];

      headerColumns.forEach(col => {
        if (col?.childrenWidth?.length) {
          const mWidths: Array<number | Array<number>> = [...col.childrenWidth];

          for (let i = 0; i < col?.children?.length; i += 1) {
            const sub = col?.children?.[i];

            if (sub?.childrenWidth) mWidths[i] = sub.childrenWidth;
          }

          widths = [...widths, ...mWidths.flat()];
        } else {
          widths = [...widths, col.width];
        }
      });

      return widths;
    },
    [],
  );

  const tableProps = useMemo(() => {
    const headerData: typeof ALL_COLUMNS = [];

    columns.forEach(c => {
      const found = ALL_COLUMNS.find(column => column.value === c);
      if (found) {
        headerData.push(found);
      }
    });

    const isFreezeFirstColumn = !'FTP'.includes(productId);

    const dynamicHeaderData = isFreezeFirstColumn
      ? headerData.slice(1)
      : headerData;

    const freezeColumnWidths = isFreezeFirstColumn
      ? [ALL_COLUMNS[0].width]
      : [];

    const columnWidths = dynamicHeaderData.map(header => header.width);
    const columnWidthRows = flatChildColumnWidthRows(dynamicHeaderData);

    return {
      headerData,
      freezeColumnWidths,
      columnWidths,
      columnWidthRows,
      childrenWidths: dynamicHeaderData.map(header => header.childrenWidth),
    };
  }, [columns, currency, flatChildColumnWidthRows, productId]);

  const scenarioOptions = useMemo(() => {
    if (
      [
        'ILB',
        'ILBP',
        'OFB',
        'EN01',
        'ILM',
        'IL8',
        'IP1',
        'IL4',
        'EN7',
        'EN9',
        'IP2',
      ].includes(productId)
    ) {
      return [
        {
          text: t('proposal:simulation.scenarioWithNumber', {
            value: '1 (5%)',
          }),
          value: '1',
        },
        {
          text: t('proposal:simulation.scenarioWithNumber', {
            value: '2 (2%)',
          }),
          value: '2',
        },
      ];
    }

    if (['EN4', 'EN3', 'EN5', 'EN6', 'TSI', 'EN8'].includes(productId)) {
      return [
        {
          text: t('proposal:simulation.scenarioWithNumber', {
            value: '1 (2%)',
          }),
          value: '1',
        },
        {
          text: t('proposal:simulation.scenarioWithNumber', {
            value: '2 (5%)',
          }),
          value: '2',
        },
      ];
    }

    if (['LVSGIO', 'LVS'].includes(productId)) {
      return [
        {
          text: t('proposal:simulation.scenarioWithNumber', {
            value: 'A (2%)',
          }),
          value: '1',
        },
        {
          text: t('proposal:simulation.scenarioWithNumber', {
            value: 'B (5%)',
          }),
          value: '2',
        },
      ];
    }

    if (['GL2', 'GL1', 'IJ7'].includes(productId)) {
      return [
        {
          text: t('proposal:simulation.scenarioWithNumber', {
            value: 'X (2%)',
          }),
          value: '1',
        },
        {
          text: t('proposal:simulation.scenarioWithNumber', {
            value: 'Y (5%)',
          }),
          value: '2',
        },
      ];
    }

    return [];
  }, [productId]);

  const hasWithdrawalAndTopup = useMemo(() => {
    return rawQuotation?.hasTopUpWithdraw ?? false;
  }, [rawQuotation]);

  return {
    isLoading,
    columns,
    tableData: convertDataTable || [],
    tableProps,
    scenarioOptions,
    hasWithdrawalAndTopup,
    summary: illustrateData?.quotation.illustration?.summaries,
  };
};

const decimalToPercentageStr = (decimalArr: number[]) => {
  return decimalArr.map(d => {
    return String(Math.round(d * 100)) + '%';
  });
};

export default useSimulationTable;
