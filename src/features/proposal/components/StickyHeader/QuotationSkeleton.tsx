import { useTheme } from '@emotion/react';
import Skeleton from 'components/Skeleton';
import { ViewStyle } from 'react-native';

type SkeletonProps = {
  width?: number;
  height?: number;
};

export function SkeletonProductImage({ height, width }: SkeletonProps) {
  const { borderRadius, colors, space } = useTheme();
  return (
    <Skeleton
      width={width || space[14]}
      height={height || space[14]}
      containerStyle={{
        backgroundColor: colors.palette.fwdOrange[20],
      }}
      radius={borderRadius.small}
    />
  );
}

export const SkeletonBlock = ({
  width,
  height,
  backgroundColor,
  ...props
}: ViewStyle) => {
  const { borderRadius, colors, space } = useTheme();
  return (
    <Skeleton
      width={width || '50%'}
      height={height || space[5]}
      containerStyle={{
        overflow: 'hidden',
        backgroundColor: backgroundColor || colors.palette.fwdOrange[20],
        ...props,
      }}
      radius={borderRadius['x-small']}
    />
  );
};
