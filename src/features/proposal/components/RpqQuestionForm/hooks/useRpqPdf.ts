import useBoundStore from 'hooks/useBoundStore';
import { useGenerateRPQPdf } from 'hooks/useGenerateRPQPdf';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useRef } from 'react';
import { PdfGenerator } from 'features/pdfViewer/components/PdfViewer';

export default function useRpqPdf() {
  const pdfGeneratorRef = useRef<PdfGenerator>(async () => ({
    base64: '',
    fileName: '',
  }));
  const { mutateAsync: generateRPQPdf } = useGenerateRPQPdf();
  const quotation = useBoundStore(state => state.quotation?.rawQuotation);
  const rpqResult = useBoundStore(state => state.quotation?.rpqResult);
  const { data: agentProfile } = useGetAgentProfile();

  pdfGeneratorRef.current = async () => {
    const pdf = await generateRPQPdf({
      outputType: 'base64',
      quotation: {
        ...quotation,
        rpq: rpqResult,
        agent: {
          agencyCode: agentProfile?.agentId || '',
          agencyName: agentProfile?.person?.fullName || '',
          agentChannelCode: agentProfile?.channel || '',
          agentCode: agentProfile?.agentId || '',
          agentEmail: agentProfile?.contact?.email || '',
          agentName: agentProfile?.person?.fullName || '',
          placeOfSigning: 'Philippines',
          secondaryFwpCode: null,
          secondaryFwpName: null,
          agentPhoneNumber: agentProfile?.contact?.mobilePhone || '',
          virtualBranch: agentProfile?.branch?.name,
        },
      },
    });

    return {
      fileName: 'RPQ',
      base64: pdf,
    };
  };

  return {
    pdfGeneratorRef,
  };
}
