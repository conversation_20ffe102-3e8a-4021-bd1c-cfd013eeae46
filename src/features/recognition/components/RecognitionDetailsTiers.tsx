import { useTheme } from '@emotion/react';
import { Icon, Row, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

export default function RecognitionDetailsTiers({
  tiersArray,
}: {
  tiersArray: {
    icon: JSX.Element;
    title: string;
    isComplete: boolean;
    isCurrentTier: boolean;
  }[];
}) {
  const { sizes, colors, borderRadius } = useTheme();
  // const {
  //   params: { title, percent },
  // } = useRootStackNRoute<'RecognitionDetails'>();
  return (
    <View
      style={{
        backgroundColor: colors.background,
        borderRadius: borderRadius['large'],
        padding: sizes[4],
      }}>
      <Typography.H7 fontWeight={'bold'} style={{ marginBottom: sizes[4] }}>
        Achieved tier
      </Typography.H7>
      <View style={{ gap: sizes[3] }}>
        {tiersArray.map((tier, index) => (
          <RecognitionTier
            key={'RecognitionTier' + tier.title + index}
            title={tier.title}
            isComplete={tier.isComplete}
            isCurrentTier={tier.isCurrentTier}
            icon={tier.icon}
          />
        ))}
      </View>
    </View>
  );
}

const RecognitionTier = ({
  icon,
  title,
  isComplete,
  isCurrentTier,
}: {
  icon: React.ReactNode;
  title: string;
  isComplete: boolean;
  isCurrentTier: boolean;
}) => {
  const { sizes, colors } = useTheme();
  const { t } = useTranslation('performance');

  return (
    <Row style={{ alignItems: 'center', justifyContent: 'space-between' }}>
      <Row>
        {icon}
        <View
          style={{
            marginLeft: sizes[3],
            gap: sizes[1],
            justifyContent: 'center',
          }}>
          {isCurrentTier && (
            <Typography.H8 color={colors.palette.fwdGreyDarkest}>
              {t('performance.recognition.currentTier')}
            </Typography.H8>
          )}
          <Typography.H8 fontWeight="bold">{title}</Typography.H8>
        </View>
      </Row>
      {isComplete ? (
        <Row style={{ gap: 5 }}>
          <Icon.TickCircle fill={colors.palette.alertGreen} />
          <Typography.Body color={colors.palette.alertGreen}>
            Complete
          </Typography.Body>
        </Row>
      ) : (
        <Row style={{ gap: 5 }}>
          <Icon.TickCircle fill={colors.palette.fwdGreyDark} />
          <Typography.Body color={colors.palette.fwdGreyDark}>
            Incomplete
          </Typography.Body>
        </Row>
      )}
    </Row>
  );
};
