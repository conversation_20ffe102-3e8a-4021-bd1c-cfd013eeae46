import styled from '@emotion/native';
import { Row } from 'cube-ui-components';
import React, { memo, useCallback, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { shallow } from 'zustand/shallow';
import DeclarationInfo from './fatca/declarationInfo/DeclarationInfo';
import Fatca from './fatca/Fatca';
import EAppFooter from '../../common/EAppFooter';
import { useTranslation } from 'react-i18next';
import Roc from './roc/Roc';
import { useEAppStore } from '../../../../utils/store/eAppStore';
import {
  fatcaFormValidationSchema,
  FatcaForm,
  initialFatcaFormData,
  rocFormValidationSchema,
  RocForm,
  initialRocFormData,
} from '../../../../validations/applicationDetails/declarationValidation';
import { useEAppValidationResolver } from '../../../../hooks/useEAppValidationResolver';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { checkFatcaRequired } from 'features/eApp/utils/planUtils';
import { useCreateCase } from 'hooks/useCreateCase';
import { useUpdateCase } from 'hooks/useUpdateCase';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetCaseManually } from 'hooks/useGetCase';
import { toFATCA, toReplacementInfo } from 'features/eApp/utils/caseUtils.my';
import { useEAppAlert } from 'features/eApp/hooks/useEAppAlert';
import { useIncompleteFields } from 'features/eApp/hooks/useIncompleteFields';
import { ValidationError } from 'yup';
import TabletSections from 'features/eAppV2/common/components/TabletSections';
import RocAcknowledgementNotice from './roc/acknowledgement/RocAcknowledgementNotice';
import RocAcknowledgementDisagreementReminder from './roc/acknowledgement/RocAcknowledgementDisagreementReminder';
import { UWType } from 'types/quotation';
import useToggle from 'hooks/useToggle';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { PartyRole } from 'types/party';

const Declaration = memo(() => {
  const { t } = useTranslation(['eApp']);
  const { alertError } = useEAppAlert();
  const [activePath, setActivePath] = useState<string | undefined>(undefined);
  const next = useEAppProgressBarStore(state => state.next);
  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const { declaration, updateFatcaDeclaration, updateRocDeclaration } =
    useEAppStore(
      state => ({
        declaration: state.my_declaration,
        updateFatcaDeclaration: state.updateFatcaDeclaration,
        updateRocDeclaration: state.updateRocDeclaration,
      }),
      shallow,
    );

  const { caseObj } = useGetActiveCase();
  const hasDecisionResponse = useMemo(() => {
    return Boolean(
      caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER))?.uw
        ?.decisionResponse,
    );
  }, [caseObj?.parties]);

  const resolverFatca = useEAppValidationResolver(fatcaFormValidationSchema);

  const quotation = useSelectedQuotation();
  const basicPlan = quotation?.plans?.[0];
  const shouldShowFatca = useMemo(
    () => checkFatcaRequired(basicPlan?.pid),
    [basicPlan],
  );

  const {
    control: fatcaFormControl,
    handleSubmit: handleFatcaFormSubmit,
    formState: { isValid: isFatcaValid },
    watch: watchFatcaForm,
    setFocus: setFocusFatcaForm,
    getValues: getFatcaValues,
  } = useForm<FatcaForm>({
    mode: 'onBlur',
    defaultValues: declaration.fatca || initialFatcaFormData,
    resolver: resolverFatca,
  });

  const resolverRoc = useEAppValidationResolver(rocFormValidationSchema);

  const {
    control: rocFormControl,
    handleSubmit: handleRocFormSubmit,
    formState: { isValid: isRocValid },
    watch: watchRocForm,
    setFocus: setFocusRocForm,
    getValues: getRocValues,
    reset,
  } = useForm<RocForm>({
    mode: 'onBlur',
    defaultValues: declaration.roc || initialRocFormData,
    resolver: resolverRoc,
  });

  const agentId = useBoundStore(state => state.auth.agentCode);
  const caseId = useBoundStore(state => state.case.caseId);
  const { mutateAsync: getCase } = useGetCaseManually();
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();

  const handleSubmit = useCallback(async () => {
    try {
      if (!caseId || !agentId) return;
      if (activePath === 'fatca') {
        setActivePath('roc');
      } else {
        const caseObj = await getCase(caseId);
        const fatcaFormData = getFatcaValues();
        const rocFormData = getRocValues();
        await saveApplication({
          caseId,
          data: {
            ...caseObj.application,
            proposerConsent: toFATCA(fatcaFormData, agentId),
            replacementInfo: toReplacementInfo(rocFormData),
          },
        });
        updateFatcaDeclaration(fatcaFormData);
        updateRocDeclaration(rocFormData);
        if (hasDecisionResponse) {
          nextGroup(true);
        } else {
          next(true);
        }
      }
    } catch {
      alertError(handleSubmit);
    }
  }, [
    activePath,
    agentId,
    alertError,
    caseId,
    getCase,
    getFatcaValues,
    getRocValues,
    hasDecisionResponse,
    next,
    nextGroup,
    saveApplication,
    updateFatcaDeclaration,
    updateRocDeclaration,
  ]);

  const fatcaScrollRef = useRef<KeyboardAwareScrollView>(null);
  const {
    totalIncompleteRequiredFields: totalFatcaIncompleteRequiredFields,
    focusOnNextIncompleteField: focusOnNextFatcaIncompleteField,
  } = useIncompleteFields({
    control: fatcaFormControl,
    watch: watchFatcaForm,
    schema: fatcaFormValidationSchema,
    scrollRef: fatcaScrollRef,
    scrollTo: option =>
      fatcaScrollRef.current?.scrollToPosition(
        0,
        option.y || 0,
        option.animated,
      ),
  });

  const rocScrollRef = useRef<KeyboardAwareScrollView>(null);
  const {
    totalIncompleteRequiredFields: totalRocIncompleteRequiredFields,
    focusOnNextIncompleteField: focusOnNextRocIncompleteField,
  } = useIncompleteFields({
    control: rocFormControl,
    watch: watchRocForm,
    schema: rocFormValidationSchema,
    scrollRef: rocScrollRef,
    scrollTo: option =>
      rocScrollRef.current?.scrollToPosition(0, option.y || 0, option.animated),
  });

  const focusOnIncompleteField = () => {
    if (activePath === 'fatca') {
      focusOnNextFatcaIncompleteField();
    } else {
      focusOnNextRocIncompleteField();
    }
  };

  const sections = useMemo(() => {
    const roc = {
      name: 'roc',
      title: t('eApp:declaration.roc'),
      content: (
        <ScrollViewContainer ref={rocScrollRef} key={'Roc'}>
          <Roc control={rocFormControl} />
          <BoxSpace />
        </ScrollViewContainer>
      ),
    };
    const fatca = {
      name: 'fatca',
      title: t('eApp:declaration.fatca'),
      content: (
        <ScrollViewContainer ref={fatcaScrollRef} key={'Fatca'}>
          <Fatca control={fatcaFormControl} />
          <DeclarationInfo />
          <BoxSpace />
        </ScrollViewContainer>
      ),
    };
    return shouldShowFatca ? [fatca, roc] : [roc];
  }, [fatcaFormControl, rocFormControl, shouldShowFatca, t]);

  const isActionDisabled =
    activePath === 'fatca'
      ? !isFatcaValid
      : activePath === 'roc'
      ? (shouldShowFatca && !isFatcaValid) || !isRocValid
      : true;

  const isGIOType = [UWType.GIO, UWType.GIO_SIO].includes(
    quotation?.plans[0].possibleUwType as UWType,
  );
  const [
    rocAcknowledgeNoticeVisible,
    showRocAcknowledgeNoticeVisible,
    hideRocAcknowledgeNoticeVisible,
  ] = useToggle();
  const [
    rocAcknowledgeDisagreementReminderVisible,
    showRocAcknowledgeDisagreementReminderVisible,
    hideRocAcknowledgeDisagreementReminderVisible,
  ] = useToggle();

  return (
    <>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooter
        progressLock="appDetail--declaration"
        onPress={async () => {
          const hasYesRoc =
            getRocValues('hasReplacePlan') === 'yes' ||
            getRocValues('terminateInfluence') === 'yes' ||
            getRocValues('isCoverageExtension') === 'yes' ||
            getRocValues('satisfiedWithExplanation') === 'yes';
          if (activePath === 'roc' && isGIOType && hasYesRoc) {
            showRocAcknowledgeNoticeVisible();
          } else {
            await handleSubmit();
          }
        }}
        disabled={isActionDisabled}
        isLoading={isSavingApplication}
        totalIncompleteRequiredFields={
          activePath === 'fatca'
            ? totalFatcaIncompleteRequiredFields
            : totalRocIncompleteRequiredFields
        }
        focusOnIncompleteField={focusOnIncompleteField}
      />
      {isGIOType && (
        <>
          <RocAcknowledgementNotice
            visible={rocAcknowledgeNoticeVisible}
            onAgree={() => {
              hideRocAcknowledgeNoticeVisible();
              handleSubmit();
            }}
            onDisagree={() => {
              hideRocAcknowledgeNoticeVisible();
              showRocAcknowledgeDisagreementReminderVisible();
              reset(initialRocFormData);
            }}
          />
          <RocAcknowledgementDisagreementReminder
            visible={rocAcknowledgeDisagreementReminderVisible}
            onDismiss={hideRocAcknowledgeDisagreementReminderVisible}
          />
        </>
      )}
    </>
  );
});

export default Declaration;

const BoxSpace = styled(Row)(({ theme: { space } }) => ({
  height: space[6],
}));

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
  }),
);
