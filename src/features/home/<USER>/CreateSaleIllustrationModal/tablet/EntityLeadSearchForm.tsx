import { useTheme } from '@emotion/react';
import IdNumberField from 'components/IdNumberField';
import Input from 'components/Input';
import { NEW_NRIC } from 'constants/optionList';
import { Box, Button, Column, Row, TextField } from 'cube-ui-components';
import {
  INVALID_FORMAT,
  MAX_NAME_LENGHT,
  REQUIRED_INPUT,
} from 'features/coverageDetails/validation/common/constant';
import React from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Lead } from 'types';

import { useValidationYupResolver } from 'utils/validation';
import * as yup from 'yup';

export const EntityLeadSearchSchema = yup.object({
  companyName: yup
    .string()
    .required(REQUIRED_INPUT)
    .max(MAX_NAME_LENGHT, INVALID_FORMAT),
  registrationNumber: yup.string().required(REQUIRED_INPUT),
});

export type EntityLeadSearchSchemaType = yup.InferType<
  typeof EntityLeadSearchSchema
>;

interface EntityLeadSearchFormProps {
  onClose: () => void;
  onFoundEntityLead: (data: Partial<Lead>) => void;
}

export default function EntityLeadSearchForm({
  onClose,
  onFoundEntityLead,
}: EntityLeadSearchFormProps) {
  const { space } = useTheme();

  const { t } = useTranslation([
    'common',
    'proposal',
    'home',
    'coverageDetails',
  ]);

  const { handleSubmit, control } = useForm<EntityLeadSearchSchemaType>({
    mode: 'onBlur',
    resolver: useValidationYupResolver(EntityLeadSearchSchema),
  });

  const onSubmit = handleSubmit(data => {
    // TODO: send xhr request to BE
    onFoundEntityLead({
      firstName: data.companyName,
      natureOfBusiness: data.registrationNumber,
    });
  });

  return (
    <Box gap={space[5]} mt={space[8]}>
      <Row gap={space[5]}>
        <Column flex={1}>
          <Input
            control={control}
            as={TextField}
            name="companyName"
            label={t('coverageDetails:formFields.companyName')}
            size={'large'}
          />
        </Column>

        <Column flex={1}>
          <Input
            control={control}
            as={IdNumberField}
            name="registrationNumber"
            label={t('coverageDetails:formFields.registrationNumberLatest')}
            size={'large'}
            idType={NEW_NRIC}
          />
        </Column>
      </Row>

      <Row justifyContent="center" marginTop={space[6]} gap={space[4]}>
        <Button
          style={{ width: 200 }}
          size="medium"
          onPress={onClose}
          text={t(
            'home:home.shortcutSection.createSaleIllustrationModal.buttonCancelText',
          )}
          variant="secondary"
        />
        <Button
          style={{ width: 200 }}
          size="medium"
          onPress={onSubmit}
          text={t(
            'home:home.shortcutSection.createSaleIllustrationModal.buttonConfirmText',
          )}
        />
      </Row>
    </Box>
  );
}
