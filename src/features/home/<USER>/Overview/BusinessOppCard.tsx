import { TouchableOpacityProps, View, useWindowDimensions } from 'react-native';
import React, { useMemo } from 'react';
import { Box, Row, Typography } from 'cube-ui-components';
import styled from '@emotion/native';
import { HomeCard, Title } from 'features/home/<USER>/utils/Common';
import BOInProgressSVG from 'features/home/<USER>/BOInProgressSVG';
import BOInAppSVG from 'features/home/<USER>/BOInAppSVG';
import BOSubmittedCasesSVG from 'features/home/<USER>/BOSubmittedCasesSVG';
import SkeletonBusinessOppCard from '../Skeleton/SkeletonBusinessOppCard';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import useBoundStore from 'hooks/useBoundStore';
import ResponsiveText from 'components/ResponsiveTypography';
import { Theme, useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import {
  useGetNewBusinessPolicyByAgentId,
  useGetNewBusinessPolicyByAgentIdPH,
} from 'hooks/useGetPolicyList';
import { useRootStackNavigation } from 'hooks/useRootStack';
import {
  BuildCountry,
  BusinessOppCardPreFilters,
  ShownMainTabParamList,
} from 'types';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useGetCaseCount } from 'hooks/useGetCase';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import BIInProgressSVG from 'features/home/<USER>/BIInProgressSVG';
import { STATUS_PENDING } from 'types/policy';
import {
  useGetSavedProposalCountMap,
  useGetSavedProposalsFilters,
} from 'hooks/useGetSavedProposalsFilters';
import {
  tableConfig,
  updateDateFilterConfig,
} from 'features/savedProposals/config';
import { sub } from 'date-fns';
import { useHasPermission } from 'hooks/useCheckClientScope';

type BusinessOppTypeKeys = 'inProgress' | 'inSi' | 'inApplication' | 'pending';

export default function BusinessOppCard() {
  const { t } = useTranslation('home');
  const { space, sizes } = useTheme();
  const { navigate } = useRootStackNavigation();
  const mainTabNavigation =
    useNavigation<NavigationProp<ShownMainTabParamList>>();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const hasPermission = useHasPermission();
  const canViewProposal = hasPermission('savedProposal');
  const canViewPolicy = hasPermission('policy');
  // const canViewPolicy = hasCountryAndDeviePermission && hasPermission('policy');

  const agentCode = useBoundStore(state => state.auth.agentCode);

  const { data: newBusList, isLoading: isNBListLoading } =
    useGetNewBusinessPolicyByAgentId(agentCode ?? '');
  const { data: newBusListPH, isLoading: isPHNBListLoading } =
    useGetNewBusinessPolicyByAgentIdPH(agentCode ?? '');

  const startDateOfProposals = useMemo(
    () =>
      updateDateFilterConfig.showFilter
        ? sub(new Date(), { days: 60 }).toISOString()
        : undefined,
    [],
  );

  const {
    data: proposalFilterCounts,
    isLoading: isProposalFilterCountLoading,
  } = useGetSavedProposalsFilters({
    start: startDateOfProposals,
    clientTypes: tableConfig.savedProposals.excludeEntity
      ? ['INDIVIDUAL']
      : undefined,
  });

  const countMap = useGetSavedProposalCountMap(proposalFilterCounts);

  // in PH #75, inProgressCount includes FNA, SI and Quick Quote
  const inProgressCount = Number(
    countMap?.FULL_SI + countMap?.FNA + countMap?.QUICK_SI,
  );

  // in MY #17 IB#62, the count includes only Full SI
  const inFullSiCount = countMap?.FULL_SI ?? 0;
  const inAllSiCount = countMap?.FULL_SI ?? 0 + countMap?.QUICK_SI ?? 0;

  const inApplicationCount = useMemo(
    () =>
      proposalFilterCounts?.latestStatusStats?.find(
        item => item.latestStatus === 'IN_APP',
      )?.count ?? 0,
    [proposalFilterCounts],
  );

  const DEFAULT_BUSINESS_OPP_CONFIG = [
    {
      // ----- PH only
      type: 'inProgress',
      // label: t('home.businessOpp.inProgress'),
      label: t(BUSINESS_OPP_CONFIG_FIRST_CARD[country].label),
      icon: <BOInProgressSVG />,
      count: inProgressCount,
      onPress: () =>
        mainTabNavigation.navigate('Proposals', {
          preFilter: BUSINESS_OPP_CONFIG_FIRST_CARD[country].preFilter,
        }),
    },
    {
      // ----- NOT in PH
      type: 'inSi',
      // label: t('home.businessOpp.inProgress'),
      label: t(BUSINESS_OPP_CONFIG_FIRST_CARD[country].label),
      icon: country === 'my' ? <BIInProgressSVG /> : <BOInProgressSVG />,
      count: inFullSiCount,
      onPress: () =>
        mainTabNavigation.navigate('Proposals', {
          preFilter: BUSINESS_OPP_CONFIG_FIRST_CARD[country].preFilter,
        }),
    },
    {
      type: 'inApplication',
      label: t('home.businessOpp.inApplication'),
      icon: <BOInAppSVG />,
      count: inApplicationCount,
      onPress: () =>
        mainTabNavigation.navigate('Proposals', {
          preFilter: 'inApplication',
        }),
    },
    {
      type: 'pending',
      label: t('home.businessOpp.submitted'),
      icon: <BOSubmittedCasesSVG size={sizes[6]} />,
      count:
        country === 'ph'
          ? newBusListPH?.length
            ? newBusListPH?.filter(data => data.status === STATUS_PENDING)
                ?.length
            : 0
          : newBusList?.pendings?.length ?? 0,
      onPress: () =>
        countryModuleSellerConfig.Policies &&
        (isTabletMode
          ? navigate('Main', {
              screen: 'Policies',
              params: {
                screen: 'NewBusiness',
                params: {
                  screen: country == 'ph' ? 'pending' : 'nbPending',
                },
              },
            })
          : navigate('PoliciesNewBusiness', {
              screen: 'NBList',
              params: {
                status: 'pending',
                viewingAgentCode: undefined,
              },
            })),
    },
  ] satisfies Array<{
    type: BusinessOppTypeKeys;
    label: string;
    icon: React.JSX.Element;
    count: number | undefined;
    onPress: () => void;
  }>;

  const BUSINESS_OPP_CONFIG = useMemo(
    () =>
      DEFAULT_BUSINESS_OPP_CONFIG.filter(({ type }) => {
        if (
          canViewProposal &&
          ((type == 'inProgress' && country == 'ph') ||
            (type == 'inSi' && country !== 'ph') ||
            type == 'inApplication')
        ) {
          return true;
        }

        if (canViewPolicy && type == 'pending') {
          return true;
        }

        return false;
        // (canViewProposal &&
        //   (type == 'inProgress' || type == 'inApplication')) ||
        // (canViewPolicy && type == 'submitted'),
      }),
    [canViewProposal, canViewPolicy, DEFAULT_BUSINESS_OPP_CONFIG],
  );

  // TODO-Mario: Remove after integration - Mock skeleton loading
  if (isNBListLoading || isProposalFilterCountLoading) {
    return <SkeletonBusinessOppCard />;
  }

  // if (BUSINESS_OPP_CONFIG?.length == 0) {
  //   return null;
  // }

  return (
    <HomeCard style={{ gap: space[4] }}>
      <TitleLabelsContainer>
        <Row
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Title fontWeight="bold">{t('home.businessOpp.title')}</Title>
        </Row>
        {countryModuleSellerConfig?.home?.businessOppCard
          ?.isReminderVisible && (
          <ReminderText>{t('home.businessOpp.reminderText')}</ReminderText>
        )}
      </TitleLabelsContainer>
      <ProspectItemsContainer isTabletMode={isTabletMode}>
        {BUSINESS_OPP_CONFIG.map(({ type, label, icon, count, onPress }) => {
          return (
            <ProspectItem
              key={`BUSINESS_OPP_` + type}
              type={type}
              label={label}
              icon={icon}
              count={count}
              onPress={onPress}
            />
          );
        })}
      </ProspectItemsContainer>
    </HomeCard>
  );
}

interface ProspectItemProps extends TouchableOpacityProps {
  type: BusinessOppTypeKeys;
  label?: string;
  icon?: React.ReactNode;
  count?: number;
  isWideScreen?: boolean;
  isNarrowScreen?: boolean;
}

function ProspectItem({
  type,
  label,
  icon,
  count,
  onPress,
}: ProspectItemProps) {
  const { isWideScreen } = useWindowAdaptationHelpers();
  const isNarrowScreen = useWindowDimensions().width <= 360;
  const { space, colors } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const hasPermission = useHasPermission();
  const canViewProposal = hasPermission('savedProposal');

  const disabled = useMemo(() => {
    if (country === 'id') {
      return !canViewProposal;
    }
    return false;
  }, [canViewProposal]);

  if (isTabletMode) {
    const isIdNBUrgent =
      country === 'id' && type === 'pending' && (count ?? 0) > 0;

    return (
      <ItemContainerTablet
        type={type}
        onPress={onPress}
        disabled={disabled}
        isWideScreen={isWideScreen}
        style={[
          isIdNBUrgent
            ? {
                padding: space[3],
                borderWidth: space[1],
                borderColor: colors.palette.alertRed,
              }
            : undefined,
        ]}>
        <IconWithUrgentContainer>
          {icon}
          {isIdNBUrgent && <UrgentLabelTablet />}
        </IconWithUrgentContainer>
        <Separator />
        <ItemLabel
          TypographyDefault={Typography.LargeBody}
          TypographyNarrow={Typography.SmallLabel}
          TypographyWide={Typography.LargeLabel}
          fontWeight={isNarrowScreen ? 'medium' : 'normal'}>
          {label}
        </ItemLabel>
        <Separator />
        <ItemCountTablet fontWeight="bold" type={type}>
          {numberToThousandsFormat(count ?? 0)}
        </ItemCountTablet>
      </ItemContainerTablet>
    );
  }

  return (
    <ItemContainer
      type={type}
      onPress={onPress}
      disabled={disabled}
      isWideScreen={isWideScreen}
      style={[
        isNarrowScreen && {
          paddingVertical: space[2],
          paddingHorizontal: space[2],
        },
      ]}
      count={count}>
      <Box>
        <IconWithUrgentContainer>
          {icon}
          {type === 'pending' && (count ?? 0) > 0 && <UrgentLabel />}
        </IconWithUrgentContainer>
        <ItemLabel
          TypographyDefault={Typography.Label}
          TypographyNarrow={Typography.SmallLabel}
          TypographyWide={Typography.LargeBody}
          fontWeight={isNarrowScreen ? 'medium' : 'normal'}>
          {label}
        </ItemLabel>
      </Box>
      <ItemCount fontWeight="bold" type={type} count={count}>
        {numberToThousandsFormat(count ?? 0)}
      </ItemCount>
    </ItemContainer>
  );
}

function UrgentLabelTablet() {
  const { t } = useTranslation('home');
  const theme = useTheme();
  return (
    <View
      style={{
        backgroundColor: theme.colors.palette.alertRed,
        paddingVertical: theme.space[1],
        paddingHorizontal: theme.space[2],
        marginLeft: theme.space[1],
        borderRadius: theme.borderRadius['x-small'],
      }}>
      <Typography.Label
        fontWeight="bold"
        style={{
          color: theme.colors.palette.alertRedLight,
        }}>
        {t('home.businessOpp.submitted.urgent')}
      </Typography.Label>
    </View>
  );
}

function UrgentLabel() {
  const { t } = useTranslation('home');
  return (
    <UrgentLabelContainer>
      <UrgentLabelText>
        {t('home.businessOpp.submitted.urgent')}
      </UrgentLabelText>
    </UrgentLabelContainer>
  );
}

const Separator = styled.View(({ theme }) => ({
  height: theme.sizes[1],
}));

const ReminderText = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.placeholder,
}));

const ProspectItemsContainer = styled(Row)(
  ({ theme, isTabletMode }: { isTabletMode: boolean; theme?: Theme }) => ({
    gap: theme?.space[isTabletMode ? 3 : 2],
    display: 'flex',
  }),
);

const ItemContainer = styled.TouchableOpacity<ProspectItemProps>(
  ({ type, theme, isWideScreen, count, disabled }) => ({
    flex: 1,
    minHeight: isWideScreen ? undefined : 140,
    backgroundColor: theme.colors.background,
    paddingVertical: theme.space[isWideScreen ? 3 : 2],
    paddingHorizontal: theme.space[isWideScreen ? 2 : 3],
    borderRadius: theme.borderRadius['x-small'],
    justifyContent: 'space-between',
    borderWidth: 2,
    borderColor:
      type === 'pending' && (count ?? 0) > 0
        ? theme.colors.palette.alertRed
        : theme.colors.background,
    opacity: disabled ? 0.5 : 1,
  }),
);
const ItemContainerTablet = styled.TouchableOpacity<ProspectItemProps>(
  ({ theme, isWideScreen, disabled }) => ({
    flex: 1,
    height: isWideScreen ? undefined : 140,
    backgroundColor: theme.colors.background,
    padding: theme.space[4],
    borderRadius: theme.borderRadius.medium,
    justifyContent: 'space-between',
    maxWidth: '34%',
    opacity: disabled ? 0.5 : 1,
  }),
);

const IconWithUrgentContainer = styled(Row)(() => ({
  alignItems: 'center',
  minHeight: 24,
}));

const ItemLabel = styled(ResponsiveText)(({ theme }) => ({
  marginTop: theme.space[2],
}));

const ItemCount = styled(Typography.ExtraLargeBody)<ProspectItemProps>(
  ({ type, theme, count }) => ({
    color:
      type === 'pending' && (count ?? 0) > 0
        ? theme.colors.palette.alertRed
        : type === 'pending'
        ? theme.colors.palette.fwdGreyDark
        : theme.colors.primary,
  }),
);

const ItemCountTablet = styled(Typography.H3)<ProspectItemProps>(
  ({ type, theme }) => ({
    color: theme.colors.primary,
    // type === 'submitted'
    //   ? theme.colors.palette.alertRed
    //   : theme.colors.primary,
  }),
);

const UrgentLabelContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.palette.alertRed,
  paddingVertical: theme.space[1] / 2,
  paddingHorizontal: theme.space[1],
  marginLeft: theme.space[1],
  borderRadius: theme.borderRadius['x-small'] / 2,
}));

const UrgentLabelText = styled(Typography.SmallLabel)(({ theme }) => ({
  color: theme.colors.palette.alertRedLight,
}));

const TitleLabelsContainer = styled.View(({ theme }) => ({
  gap: theme.space[1],
}));

type BusinessOppCardLabels =
  | 'home.businessOpp.inProgress'
  | 'home.businessOpp.inSI';

const BUSINESS_OPP_CONFIG_FIRST_CARD: Record<
  BuildCountry,
  {
    label: BusinessOppCardLabels;
    preFilter: BusinessOppCardPreFilters;
  }
> = {
  ph: {
    label: 'home.businessOpp.inProgress',
    preFilter: 'inProgress',
  },
  my: {
    label: 'home.businessOpp.inSI',
    preFilter: 'inSI',
  },
  ib: {
    label: 'home.businessOpp.inSI',
    preFilter: 'inSI',
  },
  id: {
    label: 'home.businessOpp.inSI',
    preFilter: 'inSI',
  },
} as const;
