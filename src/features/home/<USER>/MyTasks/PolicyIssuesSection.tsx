import React, { useMemo } from 'react';
import { ViewAllButton } from 'features/home/<USER>/utils/ViewAllButton';
import {
  EmptyItem,
  EmptyRecordText,
  Title,
  TitleContainer,
} from 'features/home/<USER>/utils/Common';
import { useTranslation } from 'react-i18next';
import { useGetTasks } from 'hooks/useGetTasks';
import Task from './Task';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import Animated, {
  LinearTransition,
  SlideInRight,
  SlideOutRight,
} from 'react-native-reanimated';
import { View } from 'react-native';
import { SkeletonTaskCard } from '../Skeleton/SkeletonWelcomeSection';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import { ListContainer } from './TaskStyledComponent';
import EmptyTaskSVG from 'features/home/<USER>/image/EmptyTaskSVG';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function PolicyIssuesSection({
  isSkeletonShown = false,
  onViewAllPress,
  EmptyRecordComponent,
}: {
  isSkeletonShown?: boolean;
  onViewAllPress?: () => void;
  EmptyRecordComponent?: React.ReactNode;
}) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { t } = useTranslation('home');
  const { data, isLoading } = useGetTasks();
  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();

  const policyTasks = useMemo(() => {
    const uncompletedTasks =
      data?.policyIssueList.filter(task => !task.isCompleted) || [];

    return uncompletedTasks || [];
  }, [data?.policyIssueList]);

  const policyTaskTopThree = policyTasks.slice(0, 3);

  const policyTaskListCount = policyTasks.length || 0;

  return (
    <View>
      <TitleContainer>
        <Title fontWeight="bold">{t('task.policy.title')}</Title>
        <ViewAllButton
          showCount={
            country === 'ph'
              ? policyTaskListCount > 0
              : !isTabletMode && policyTaskListCount > 0
          }
          count={policyTaskListCount}
          onPress={() => {
            if (countryModuleSellerConfig.Policies) {
              onViewAllPress
                ? onViewAllPress()
                : navigate('PoliciesNewBusiness', {
                    screen: 'NBList',
                    params: {
                      status: 'pending',
                      viewingAgentCode: undefined,
                    },
                  });
            } else {
              console.log(
                '====>🚀 ~ file: PolicyIssuesSection.tsx:61 ~ countryModuleSellerConfig.Policies: ',
                countryModuleSellerConfig.Policies,
                '== No navigation should be done',
              );
            }
          }}
          typography={isTabletMode ? 'H8' : 'H7'}
          fontWeight={isTabletMode ? 'bold' : 'medium'}
        />
      </TitleContainer>

      {isLoading && isSkeletonShown ? (
        <SkeletonTaskCard />
      ) : policyTaskTopThree.length ? (
        <ListContainer>
          {policyTaskTopThree.map((task, idx) => {
            return (
              <Animated.View
                layout={LinearTransition}
                entering={SlideInRight}
                exiting={SlideOutRight}
                key={'policyTask_' + (task.taskId ?? idx)}>
                <Task.PolicyIssue {...task} />
              </Animated.View>
            );
          })}
        </ListContainer>
      ) : EmptyRecordComponent ? (
        EmptyRecordComponent
      ) : (
        <EmptyItem>
          <EmptyTaskSVG />
          <EmptyRecordText>{t('task.policy.emptyCase')}</EmptyRecordText>
        </EmptyItem>
      )}
    </View>
  );
}
